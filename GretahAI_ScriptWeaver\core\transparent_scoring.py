"""
Transparent Script Quality Scoring System

This module implements a scientifically-backed, transparent scoring methodology
for test script validation that replaces the black box AI scoring with auditable,
rule-based scoring criteria.

The scoring system is based on the 10-point validation criteria used in the AI prompts
but provides deterministic, explainable scoring with detailed breakdowns.

Author: GretahAI ScriptWeaver
"""

import re
import ast
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ScoreBreakdown:
    """Detailed breakdown of scoring for transparency."""
    category: str
    max_points: int
    earned_points: int
    percentage: float
    issues: List[str]
    recommendations: List[str]
    details: str


@dataclass
class TransparentScoreResult:
    """Complete transparent scoring result."""
    total_score: int
    max_possible_score: int
    percentage: float
    breakdowns: List[ScoreBreakdown]
    overall_issues: List[Dict[str, Any]]
    overall_recommendations: List[str]
    ready_for_execution: bool
    confidence_rating: str


class TransparentScorer:
    """
    Transparent, rule-based scoring system for test script validation.

    This class implements a scientifically-backed scoring methodology that provides
    auditable, explainable quality scores based on deterministic rules rather than
    black box AI analysis.
    """

    # Scientific scoring weights based on test automation best practices
    SCORING_CRITERIA = {
        "syntax": {"max_points": 15, "weight": 0.15},           # 15% - Critical foundation
        "webdriver_usage": {"max_points": 15, "weight": 0.15}, # 15% - Framework compliance
        "locator_strategy": {"max_points": 15, "weight": 0.15}, # 15% - Maintainability
        "wait_conditions": {"max_points": 10, "weight": 0.10},  # 10% - Reliability
        "test_data_integration": {"max_points": 10, "weight": 0.10}, # 10% - Data handling
        "action_sequencing": {"max_points": 10, "weight": 0.10}, # 10% - Logic flow
        "error_handling": {"max_points": 8, "weight": 0.08},    # 8% - Robustness
        "assertions": {"max_points": 12, "weight": 0.12},       # 12% - Verification
        "best_practices": {"max_points": 3, "weight": 0.03},    # 3% - Code quality
        "integration": {"max_points": 2, "weight": 0.02}        # 2% - Compatibility
    }

    def __init__(self):
        """Initialize the transparent scorer."""
        self.total_max_points = sum(criteria["max_points"] for criteria in self.SCORING_CRITERIA.values())
        logger.info(f"TransparentScorer initialized with {self.total_max_points} total possible points")

    def score_script(
        self,
        script_content: str,
        test_case: Dict[str, Any],
        step_table_entry: Optional[Dict[str, Any]] = None,
        test_data: Optional[Dict[str, Any]] = None,
        element_matches: Optional[Dict[str, Any]] = None
    ) -> TransparentScoreResult:
        """
        Score a test script using transparent, rule-based criteria.

        Args:
            script_content: The test script to score
            test_case: Test case information for context
            step_table_entry: Step table entry for context
            test_data: Test data used in the script
            element_matches: Element matches used in the script

        Returns:
            TransparentScoreResult: Complete scoring breakdown
        """
        logger.info(f"Starting transparent scoring for test case {test_case.get('Test Case ID', 'Unknown')}")

        breakdowns = []
        overall_issues = []
        overall_recommendations = []

        # Score each category
        breakdowns.append(self._score_syntax(script_content))
        breakdowns.append(self._score_webdriver_usage(script_content))
        breakdowns.append(self._score_locator_strategy(script_content))
        breakdowns.append(self._score_wait_conditions(script_content))
        breakdowns.append(self._score_test_data_integration(script_content, test_data))
        breakdowns.append(self._score_action_sequencing(script_content))
        breakdowns.append(self._score_error_handling(script_content))
        breakdowns.append(self._score_assertions(script_content, step_table_entry))
        breakdowns.append(self._score_best_practices(script_content))
        breakdowns.append(self._score_integration(script_content))

        # Calculate total score
        total_earned = sum(breakdown.earned_points for breakdown in breakdowns)
        percentage = (total_earned / self.total_max_points) * 100

        # Collect all issues and recommendations
        for breakdown in breakdowns:
            for issue in breakdown.issues:
                overall_issues.append({
                    "category": breakdown.category.lower().replace(" ", "_"),
                    "severity": self._determine_severity(breakdown.percentage),
                    "description": issue
                })
            overall_recommendations.extend(breakdown.recommendations)

        # Determine readiness and confidence
        ready_for_execution = percentage >= 70 and all(
            breakdown.percentage >= 50 for breakdown in breakdowns
            if breakdown.category in ["Syntax", "WebDriver Usage", "Assertions"]
        )

        confidence_rating = "high" if percentage >= 80 else "medium" if percentage >= 60 else "low"

        result = TransparentScoreResult(
            total_score=int(percentage),
            max_possible_score=100,
            percentage=percentage,
            breakdowns=breakdowns,
            overall_issues=overall_issues,
            overall_recommendations=list(set(overall_recommendations)),  # Remove duplicates
            ready_for_execution=ready_for_execution,
            confidence_rating=confidence_rating
        )

        logger.info(f"Transparent scoring completed: {int(percentage)}/100 ({confidence_rating} confidence)")
        return result

    def _determine_severity(self, percentage: float) -> str:
        """Determine issue severity based on category performance."""
        if percentage >= 80:
            return "low"
        elif percentage >= 60:
            return "medium"
        else:
            return "high"

    def _score_syntax(self, script_content: str) -> ScoreBreakdown:
        """Score syntax correctness (15 points max)."""
        max_points = self.SCORING_CRITERIA["syntax"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check Python syntax
        try:
            ast.parse(script_content)
            details.append("✓ Python syntax is valid")
        except SyntaxError as e:
            earned_points -= 10
            issues.append(f"Python syntax error: {str(e)}")
            recommendations.append("Fix Python syntax errors")
            details.append("✗ Python syntax errors detected")

        # Check for required imports
        required_imports = ["pytest", "selenium", "webdriver"]
        missing_imports = []

        for imp in required_imports:
            if imp not in script_content:
                missing_imports.append(imp)

        if missing_imports:
            deduction = min(3, len(missing_imports))
            earned_points -= deduction
            issues.append(f"Missing imports: {', '.join(missing_imports)}")
            recommendations.append("Add missing required imports")
            details.append(f"✗ Missing {len(missing_imports)} required imports")
        else:
            details.append("✓ All required imports present")

        # Check for basic structure
        if "def test_" not in script_content:
            earned_points -= 2
            issues.append("No test function found")
            recommendations.append("Add a proper test function with 'def test_' naming")
            details.append("✗ No test function found")
        else:
            details.append("✓ Test function structure present")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Syntax",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_webdriver_usage(self, script_content: str) -> ScoreBreakdown:
        """Score WebDriver usage correctness (15 points max)."""
        max_points = self.SCORING_CRITERIA["webdriver_usage"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check for correct browser fixture usage
        if "def test_" in script_content:
            # Check if browser parameter is used correctly
            if re.search(r'def test_\w*\([^)]*browser[^)]*\)', script_content):
                details.append("✓ Uses 'browser' fixture parameter")
            else:
                earned_points -= 8
                issues.append("Test function should use 'browser' fixture parameter")
                recommendations.append("Change function parameter to use 'browser' instead of 'driver'")
                details.append("✗ Missing 'browser' fixture parameter")

            # Check for incorrect 'driver' usage
            if re.search(r'def test_\w*\([^)]*driver[^)]*\)', script_content):
                earned_points -= 5
                issues.append("Using deprecated 'driver' parameter instead of 'browser'")
                recommendations.append("Replace 'driver' parameter with 'browser'")
                details.append("✗ Uses deprecated 'driver' parameter")

        # Check for proper WebDriver method usage
        webdriver_methods = ["find_element", "click", "send_keys", "get"]
        method_usage = sum(1 for method in webdriver_methods if f"browser.{method}" in script_content)

        if method_usage > 0:
            details.append(f"✓ Uses {method_usage} WebDriver methods correctly")
        else:
            earned_points -= 2
            issues.append("No WebDriver methods found")
            recommendations.append("Add WebDriver interactions (find_element, click, send_keys, etc.)")
            details.append("✗ No WebDriver methods detected")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="WebDriver Usage",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_locator_strategy(self, script_content: str) -> ScoreBreakdown:
        """Score locator strategy quality (15 points max)."""
        max_points = self.SCORING_CRITERIA["locator_strategy"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Count different locator types
        css_selectors = len(re.findall(r'By\.CSS_SELECTOR|css_selector', script_content, re.IGNORECASE))
        xpath_selectors = len(re.findall(r'By\.XPATH|xpath', script_content, re.IGNORECASE))
        id_selectors = len(re.findall(r'By\.ID|by_id', script_content, re.IGNORECASE))
        name_selectors = len(re.findall(r'By\.NAME|by_name', script_content, re.IGNORECASE))

        total_selectors = css_selectors + xpath_selectors + id_selectors + name_selectors

        if total_selectors == 0:
            earned_points -= 10
            issues.append("No element locators found")
            recommendations.append("Add element locators using By.ID, By.CSS_SELECTOR, etc.")
            details.append("✗ No locators detected")
        else:
            details.append(f"✓ Found {total_selectors} locators")

            # Prefer ID and CSS selectors over XPath
            if id_selectors > 0:
                details.append(f"✓ Uses {id_selectors} ID selectors (excellent)")

            if css_selectors > 0:
                details.append(f"✓ Uses {css_selectors} CSS selectors (good)")

            # Penalize excessive XPath usage
            if xpath_selectors > total_selectors * 0.7:
                earned_points -= 3
                issues.append("Overuse of XPath selectors")
                recommendations.append("Replace XPath selectors with CSS selectors or ID when possible")
                details.append(f"⚠ High XPath usage ({xpath_selectors}/{total_selectors})")

            # Check for generic/brittle locators
            generic_patterns = [
                r'//div\[\d+\]',  # Generic div with index
                r'//\*\[\d+\]',   # Generic element with index
                r'\.click\(\)',   # Click without locator
            ]

            generic_count = sum(len(re.findall(pattern, script_content)) for pattern in generic_patterns)
            if generic_count > 0:
                earned_points -= min(4, generic_count * 2)
                issues.append(f"Found {generic_count} generic/brittle locators")
                recommendations.append("Use more specific locators with stable attributes")
                details.append(f"⚠ {generic_count} generic locators detected")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Locator Strategy",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_wait_conditions(self, script_content: str) -> ScoreBreakdown:
        """Score wait conditions implementation (10 points max)."""
        max_points = self.SCORING_CRITERIA["wait_conditions"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check for WebDriverWait usage
        wait_imports = ["WebDriverWait", "expected_conditions"]
        wait_usage = sum(1 for wait_type in wait_imports if wait_type in script_content)

        if wait_usage == 0:
            earned_points -= 6
            issues.append("No explicit waits found")
            recommendations.append("Add WebDriverWait with expected_conditions for reliable element interactions")
            details.append("✗ No explicit waits detected")
        else:
            details.append(f"✓ Uses {wait_usage} wait mechanisms")

            # Check for proper wait conditions
            wait_conditions = [
                "visibility_of_element_located",
                "element_to_be_clickable",
                "presence_of_element_located",
                "text_to_be_present_in_element"
            ]

            condition_usage = sum(1 for condition in wait_conditions if condition in script_content)
            if condition_usage > 0:
                details.append(f"✓ Uses {condition_usage} proper wait conditions")
            else:
                earned_points -= 2
                issues.append("WebDriverWait found but no proper expected conditions")
                recommendations.append("Use specific expected conditions like visibility_of_element_located")
                details.append("⚠ Missing proper wait conditions")

        # Check for sleep usage (bad practice)
        sleep_usage = len(re.findall(r'time\.sleep|sleep\(', script_content))
        if sleep_usage > 0:
            earned_points -= min(3, sleep_usage)
            issues.append(f"Found {sleep_usage} hard sleeps")
            recommendations.append("Replace time.sleep() with explicit WebDriverWait")
            details.append(f"⚠ {sleep_usage} hard sleeps detected")

        # Check for appropriate timeout values
        timeout_pattern = r'WebDriverWait\([^,]+,\s*(\d+)'
        timeouts = re.findall(timeout_pattern, script_content)
        if timeouts:
            timeout_values = [int(t) for t in timeouts]
            reasonable_timeouts = [t for t in timeout_values if 5 <= t <= 30]
            if len(reasonable_timeouts) == len(timeout_values):
                details.append("✓ Appropriate timeout values")
            else:
                earned_points -= 1
                issues.append("Some timeout values may be inappropriate")
                recommendations.append("Use timeout values between 5-30 seconds")
                details.append("⚠ Some inappropriate timeout values")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Wait Conditions",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_test_data_integration(self, script_content: str, test_data: Optional[Dict[str, Any]]) -> ScoreBreakdown:
        """Score test data integration (10 points max)."""
        max_points = self.SCORING_CRITERIA["test_data_integration"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check if test_data is expected to be used
        if test_data and len(test_data) > 0:
            # Check for test_data usage in script
            test_data_usage = "test_data[" in script_content or "test_data.get(" in script_content

            if test_data_usage:
                details.append("✓ Uses test_data fixture correctly")

                # Check for proper key access
                for key in test_data.keys():
                    if f'test_data["{key}"]' in script_content or f"test_data.get('{key}')" in script_content:
                        details.append(f"✓ Accesses test_data key: {key}")
                    else:
                        earned_points -= 1
                        issues.append(f"Test data key '{key}' not used in script")
                        recommendations.append(f"Use test_data['{key}'] to access manual input data")
            else:
                earned_points -= 6
                issues.append("Test data available but not used in script")
                recommendations.append("Access manual input data using test_data fixture")
                details.append("✗ Test data not integrated")

            # Check for placeholder values
            placeholder_patterns = [
                r'test_data\["[^"]*"\]\s*=\s*"[Pp]laceholder',
                r'test_data\["[^"]*"\]\s*=\s*"[Aa]ny_',
                r'test_data\["[^"]*"\]\s*=\s*"[Tt]est_'
            ]

            placeholder_count = sum(len(re.findall(pattern, script_content)) for pattern in placeholder_patterns)
            if placeholder_count > 0:
                earned_points -= min(2, placeholder_count)
                issues.append(f"Found {placeholder_count} placeholder values in test data")
                recommendations.append("Replace placeholder values with actual test data")
                details.append(f"⚠ {placeholder_count} placeholder values detected")
        else:
            # No test data expected, check that script doesn't incorrectly reference it
            if "test_data[" in script_content:
                earned_points -= 3
                issues.append("Script references test_data but none provided")
                recommendations.append("Remove test_data references or provide test data")
                details.append("⚠ Unexpected test_data usage")
            else:
                details.append("✓ No test data required or used")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Test Data Integration",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_action_sequencing(self, script_content: str) -> ScoreBreakdown:
        """Score action sequencing logic (10 points max)."""
        max_points = self.SCORING_CRITERIA["action_sequencing"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check for logical action sequence: wait → interact → assert
        lines = script_content.split('\n')

        # Look for interaction patterns
        wait_patterns = ["WebDriverWait", "visibility_of", "element_to_be_clickable"]
        interaction_patterns = ["click()", "send_keys(", "clear()", "submit()"]
        assertion_patterns = ["assert ", "assertEqual", "assertTrue", "assertIn"]

        wait_lines = [i for i, line in enumerate(lines) if any(pattern in line for pattern in wait_patterns)]
        interaction_lines = [i for i, line in enumerate(lines) if any(pattern in line for pattern in interaction_patterns)]
        assertion_lines = [i for i, line in enumerate(lines) if any(pattern in line for pattern in assertion_patterns)]

        # Check sequence logic
        if wait_lines and interaction_lines:
            # Check if waits generally come before interactions
            proper_sequences = 0
            for interaction_line in interaction_lines:
                preceding_waits = [w for w in wait_lines if w < interaction_line and interaction_line - w <= 5]
                if preceding_waits:
                    proper_sequences += 1

            if proper_sequences > 0:
                details.append(f"✓ {proper_sequences} proper wait→interact sequences")
            else:
                earned_points -= 3
                issues.append("Interactions without preceding waits")
                recommendations.append("Add waits before element interactions")
                details.append("⚠ Missing wait→interact sequences")

        # Check for assertions after interactions
        if interaction_lines and assertion_lines:
            assertions_after_interactions = sum(1 for assertion_line in assertion_lines
                                              if any(interaction_line < assertion_line
                                                    for interaction_line in interaction_lines))
            if assertions_after_interactions > 0:
                details.append(f"✓ {assertions_after_interactions} assertions after interactions")
            else:
                earned_points -= 2
                issues.append("No assertions found after interactions")
                recommendations.append("Add assertions to verify interaction results")
                details.append("⚠ Missing interact→assert sequences")

        # Check for proper navigation sequence
        if "browser.get(" in script_content:
            get_line = next((i for i, line in enumerate(lines) if "browser.get(" in line), -1)
            if get_line >= 0:
                # Check if interactions come after navigation
                interactions_after_nav = [i for i in interaction_lines if i > get_line]
                if interactions_after_nav:
                    details.append("✓ Interactions after navigation")
                else:
                    earned_points -= 1
                    issues.append("No interactions found after navigation")
                    recommendations.append("Add element interactions after page navigation")
                    details.append("⚠ Missing navigation→interact sequence")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Action Sequencing",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_error_handling(self, script_content: str) -> ScoreBreakdown:
        """Score error handling implementation (8 points max)."""
        max_points = self.SCORING_CRITERIA["error_handling"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check for try/except blocks
        try_count = script_content.count("try:")
        except_count = script_content.count("except")

        if try_count > 0 and except_count > 0:
            details.append(f"✓ Has {try_count} try/except blocks")

            # Check for specific exception handling
            if "except Exception" in script_content:
                details.append("✓ Handles general exceptions")
            elif "except " in script_content:
                details.append("✓ Has specific exception handling")
            else:
                earned_points -= 1
                issues.append("Try block without proper except clause")
                recommendations.append("Add proper except clause to handle exceptions")
                details.append("⚠ Incomplete exception handling")
        else:
            earned_points -= 4
            issues.append("No error handling found")
            recommendations.append("Add try/except blocks around test operations")
            details.append("✗ No error handling detected")

        # Check for proper error logging or handling
        if "logger." in script_content or "print(" in script_content:
            details.append("✓ Includes error logging")
        else:
            earned_points -= 1
            issues.append("No error logging found")
            recommendations.append("Add logging for better error diagnosis")
            details.append("⚠ No error logging")

        # Penalize bare except clauses
        bare_except_count = len(re.findall(r'except\s*:', script_content))
        if bare_except_count > 0:
            earned_points -= min(2, bare_except_count)
            issues.append(f"Found {bare_except_count} bare except clauses")
            recommendations.append("Use specific exception types instead of bare except")
            details.append(f"⚠ {bare_except_count} bare except clauses")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Error Handling",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_assertions(self, script_content: str, step_table_entry: Optional[Dict[str, Any]]) -> ScoreBreakdown:
        """Score assertions implementation (12 points max)."""
        max_points = self.SCORING_CRITERIA["assertions"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Count different types of assertions
        assertion_patterns = [
            r'assert\s+',
            r'assertEqual\(',
            r'assertTrue\(',
            r'assertFalse\(',
            r'assertIn\(',
            r'assertNotIn\('
        ]

        assertion_count = sum(len(re.findall(pattern, script_content)) for pattern in assertion_patterns)

        if assertion_count == 0:
            earned_points -= 8
            issues.append("No assertions found")
            recommendations.append("Add at least one meaningful assertion to verify test results")
            details.append("✗ No assertions detected")
        else:
            details.append(f"✓ Found {assertion_count} assertions")

            # Check for meaningful assertions (not just assert True)
            meaningful_assertions = [
                r'assert\s+[^T][^r][^u][^e]',  # Not "assert True"
                r'assert\s+"[^"]*"\s+in\s+',
                r'assert\s+\w+\.',
                r'assertEqual\(',
                r'assertIn\('
            ]

            meaningful_count = sum(len(re.findall(pattern, script_content)) for pattern in meaningful_assertions)
            if meaningful_count > 0:
                details.append(f"✓ {meaningful_count} meaningful assertions")
            else:
                earned_points -= 4
                issues.append("Only trivial assertions found (e.g., assert True)")
                recommendations.append("Add specific assertions that verify actual test outcomes")
                details.append("⚠ Only trivial assertions")

        # Check if assertions relate to expected results
        if step_table_entry and step_table_entry.get('expected'):
            expected_result = step_table_entry['expected'].lower()

            # Look for assertions that might relate to expected results
            if any(keyword in script_content.lower() for keyword in ['url', 'title', 'text', 'visible', 'displayed']):
                details.append("✓ Assertions relate to expected outcomes")
            else:
                earned_points -= 2
                issues.append("Assertions don't seem to verify expected results")
                recommendations.append("Add assertions that verify the expected test outcomes")
                details.append("⚠ Assertions don't match expected results")

        # Check for proper assertion placement (after interactions)
        lines = script_content.split('\n')
        interaction_lines = [i for i, line in enumerate(lines)
                           if any(pattern in line for pattern in ["click()", "send_keys(", "submit()"])]
        assertion_lines = [i for i, line in enumerate(lines)
                          if any(pattern in line for pattern in assertion_patterns)]

        if interaction_lines and assertion_lines:
            assertions_after_interactions = sum(1 for assertion_line in assertion_lines
                                              if any(interaction_line < assertion_line
                                                    for interaction_line in interaction_lines))
            if assertions_after_interactions == 0:
                earned_points -= 1
                issues.append("No assertions found after interactions")
                recommendations.append("Place assertions after interactions to verify results")
                details.append("⚠ Poor assertion placement")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Assertions",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_best_practices(self, script_content: str) -> ScoreBreakdown:
        """Score pytest best practices (3 points max)."""
        max_points = self.SCORING_CRITERIA["best_practices"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check function naming convention
        if re.search(r'def test_\w+\(', script_content):
            details.append("✓ Proper test function naming")
        else:
            earned_points -= 1
            issues.append("Test function doesn't follow naming convention")
            recommendations.append("Name test functions starting with 'test_'")
            details.append("✗ Poor function naming")

        # Check for docstrings
        if '"""' in script_content or "'''" in script_content:
            details.append("✓ Includes docstrings")
        else:
            earned_points -= 1
            issues.append("No docstrings found")
            recommendations.append("Add docstrings to document test purpose")
            details.append("⚠ Missing docstrings")

        # Check for proper imports organization
        import_lines = [line.strip() for line in script_content.split('\n') if line.strip().startswith('import') or line.strip().startswith('from')]
        if len(import_lines) > 0:
            # Check if imports are at the top
            first_import_line = next((i for i, line in enumerate(script_content.split('\n'))
                                    if line.strip().startswith('import') or line.strip().startswith('from')), -1)
            non_comment_lines_before = [line for line in script_content.split('\n')[:first_import_line]
                                      if line.strip() and not line.strip().startswith('#')]

            if len(non_comment_lines_before) <= 1:  # Allow for module docstring
                details.append("✓ Imports properly organized")
            else:
                earned_points -= 1
                issues.append("Imports not at top of file")
                recommendations.append("Move imports to the top of the file")
                details.append("⚠ Poor import organization")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Best Practices",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )

    def _score_integration(self, script_content: str) -> ScoreBreakdown:
        """Score integration compatibility (2 points max)."""
        max_points = self.SCORING_CRITERIA["integration"]["max_points"]
        earned_points = max_points
        issues = []
        recommendations = []
        details = []

        # Check for conftest.py compatibility
        if "browser" in script_content and "def test_" in script_content:
            details.append("✓ Compatible with conftest.py fixtures")
        else:
            earned_points -= 1
            issues.append("May not be compatible with conftest.py")
            recommendations.append("Ensure test uses browser fixture from conftest.py")
            details.append("⚠ Potential conftest.py compatibility issues")

        # Check for screenshot handling compatibility
        if "screenshot" not in script_content or "save_screenshot" not in script_content:
            details.append("✓ Relies on conftest.py screenshot handling")
        else:
            earned_points -= 1
            issues.append("Custom screenshot handling may conflict with conftest.py")
            recommendations.append("Remove custom screenshot code, use conftest.py handling")
            details.append("⚠ Custom screenshot handling detected")

        percentage = (earned_points / max_points) * 100

        return ScoreBreakdown(
            category="Integration",
            max_points=max_points,
            earned_points=max(0, earned_points),
            percentage=percentage,
            issues=issues,
            recommendations=recommendations,
            details=" | ".join(details)
        )


def create_transparent_validation_result(
    transparent_result: TransparentScoreResult,
    ai_recommendations: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Convert transparent scoring result to the format expected by the validation system.

    Args:
        transparent_result: The transparent scoring result
        ai_recommendations: Optional AI-generated recommendations to supplement

    Returns:
        Dict: Validation result in the expected format
    """
    # Combine transparent and AI recommendations
    all_recommendations = transparent_result.overall_recommendations.copy()
    if ai_recommendations:
        all_recommendations.extend(ai_recommendations)

    return {
        "quality_score": transparent_result.total_score,
        "syntax_valid": all(breakdown.category != "Syntax" or breakdown.percentage >= 80
                           for breakdown in transparent_result.breakdowns),
        "issues_found": transparent_result.overall_issues,
        "recommendations": list(set(all_recommendations)),  # Remove duplicates
        "confidence_rating": transparent_result.confidence_rating,
        "ready_for_execution": transparent_result.ready_for_execution,
        "transparent_scoring": {
            "enabled": True,
            "total_score": transparent_result.total_score,
            "max_possible_score": transparent_result.max_possible_score,
            "percentage": transparent_result.percentage,
            "breakdown": [
                {
                    "category": breakdown.category,
                    "max_points": breakdown.max_points,
                    "earned_points": breakdown.earned_points,
                    "percentage": breakdown.percentage,
                    "details": breakdown.details,
                    "issues": breakdown.issues,
                    "recommendations": breakdown.recommendations
                }
                for breakdown in transparent_result.breakdowns
            ]
        }
    }
