#!/usr/bin/env python3
"""
Test script for the transparent scoring system.

This script validates that the transparent scoring system works correctly
and produces consistent, auditable results.
"""

import sys
import os
import json
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.transparent_scoring import TransparentScorer, create_transparent_validation_result


def test_high_quality_script():
    """Test scoring of a high-quality script."""
    print("Testing high-quality script...")

    script_content = '''
import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_login_functionality(browser):
    """Test user login functionality."""
    try:
        # Navigate to login page
        browser.get("https://example.com/login")

        # Wait for and interact with username field
        username_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located((By.ID, "username"))
        )
        username_field.send_keys("test_user")

        # Wait for and interact with password field
        password_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located((By.ID, "password"))
        )
        password_field.send_keys("test_password")

        # Click login button
        login_button = WebDriverWait(browser, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "#login-btn"))
        )
        login_button.click()

        # Verify successful login
        WebDriverWait(browser, 10).until(
            EC.url_contains("dashboard")
        )
        assert "dashboard" in browser.current_url

    except Exception as e:
        logger.error(f"Login test failed: {e}")
        raise
'''

    test_case = {"Test Case ID": "TC001"}
    step_table_entry = {"step_no": "1", "action": "Login", "expected": "User is logged in"}
    test_data = {"username": "test_user", "password": "test_password"}

    scorer = TransparentScorer()
    result = scorer.score_script(script_content, test_case, step_table_entry, test_data)

    print(f"High-quality script score: {result.total_score}/100 ({result.percentage:.1f}%)")
    print(f"Ready for execution: {result.ready_for_execution}")
    print(f"Confidence: {result.confidence_rating}")

    # Should score well (80+)
    assert result.total_score >= 80, f"Expected high score (80+), got {result.total_score}"
    assert result.ready_for_execution, "High-quality script should be ready for execution"

    print("✅ High-quality script test passed")
    return result


def test_low_quality_script():
    """Test scoring of a low-quality script."""
    print("\nTesting low-quality script...")

    script_content = '''
def test_something():
    driver.get("http://example.com")
    driver.find_element_by_xpath("//div[1]").click()
    assert True
'''

    test_case = {"Test Case ID": "TC002"}
    step_table_entry = {"step_no": "1", "action": "Click something", "expected": "Something happens"}

    scorer = TransparentScorer()
    result = scorer.score_script(script_content, test_case, step_table_entry)

    print(f"Low-quality script score: {result.total_score}/100 ({result.percentage:.1f}%)")
    print(f"Ready for execution: {result.ready_for_execution}")
    print(f"Confidence: {result.confidence_rating}")
    print(f"Issues found: {len(result.overall_issues)}")

    # Should score poorly (below 70 and not ready for execution)
    assert result.total_score < 70, f"Expected low score (<70), got {result.total_score}"
    assert not result.ready_for_execution, "Low-quality script should not be ready for execution"
    assert len(result.overall_issues) > 0, "Low-quality script should have issues"

    print("✅ Low-quality script test passed")
    return result


def test_validation_result_format():
    """Test that the validation result format is correct."""
    print("\nTesting validation result format...")

    script_content = '''
import pytest
from selenium.webdriver.common.by import By

def test_example(browser):
    browser.get("https://example.com")
    assert "example" in browser.title
'''

    test_case = {"Test Case ID": "TC003"}

    scorer = TransparentScorer()
    transparent_result = scorer.score_script(script_content, test_case)

    # Convert to validation format
    validation_result = create_transparent_validation_result(transparent_result)

    # Check required keys
    required_keys = [
        'quality_score', 'syntax_valid', 'issues_found', 'recommendations',
        'confidence_rating', 'ready_for_execution', 'transparent_scoring'
    ]

    for key in required_keys:
        assert key in validation_result, f"Missing required key: {key}"

    # Check transparent scoring data
    transparent_data = validation_result['transparent_scoring']
    assert transparent_data['enabled'] == True
    assert 'breakdown' in transparent_data
    assert len(transparent_data['breakdown']) == 10  # 10 scoring categories

    print("✅ Validation result format test passed")
    return validation_result


def test_scoring_breakdown():
    """Test detailed scoring breakdown."""
    print("\nTesting scoring breakdown...")

    script_content = '''
import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By

def test_search(browser):
    browser.get("https://example.com")
    search_box = browser.find_element(By.ID, "search")
    search_box.send_keys("test query")
    assert "search" in browser.current_url
'''

    test_case = {"Test Case ID": "TC004"}

    scorer = TransparentScorer()
    result = scorer.score_script(script_content, test_case)

    print(f"\nDetailed breakdown for script scoring {result.total_score}/100:")
    for breakdown in result.breakdowns:
        print(f"  {breakdown.category}: {breakdown.earned_points}/{breakdown.max_points} pts ({breakdown.percentage:.1f}%)")
        if breakdown.issues:
            for issue in breakdown.issues:
                print(f"    ⚠️ {issue}")
        if breakdown.recommendations:
            for rec in breakdown.recommendations:
                print(f"    💡 {rec}")

    # Verify all categories are present
    expected_categories = [
        "Syntax", "WebDriver Usage", "Locator Strategy", "Wait Conditions",
        "Test Data Integration", "Action Sequencing", "Error Handling",
        "Assertions", "Best Practices", "Integration"
    ]

    actual_categories = [breakdown.category for breakdown in result.breakdowns]
    for category in expected_categories:
        assert category in actual_categories, f"Missing category: {category}"

    print("✅ Scoring breakdown test passed")
    return result


def main():
    """Run all tests."""
    print("🧪 Testing Transparent Scoring System")
    print("=" * 50)

    try:
        # Run tests
        high_quality_result = test_high_quality_script()
        low_quality_result = test_low_quality_script()
        validation_format_result = test_validation_result_format()
        breakdown_result = test_scoring_breakdown()

        print("\n" + "=" * 50)
        print("📊 Test Summary:")
        print(f"High-quality script: {high_quality_result.total_score}/100")
        print(f"Low-quality script: {low_quality_result.total_score}/100")
        print(f"Validation format: ✅ Passed")
        print(f"Scoring breakdown: ✅ Passed")

        print("\n✅ All tests passed! Transparent scoring system is working correctly.")

        # Save sample results for inspection
        sample_results = {
            "high_quality": {
                "score": high_quality_result.total_score,
                "breakdown": [
                    {
                        "category": b.category,
                        "points": f"{b.earned_points}/{b.max_points}",
                        "percentage": f"{b.percentage:.1f}%"
                    }
                    for b in high_quality_result.breakdowns
                ]
            },
            "low_quality": {
                "score": low_quality_result.total_score,
                "issues_count": len(low_quality_result.overall_issues),
                "recommendations_count": len(low_quality_result.overall_recommendations)
            }
        }

        with open("transparent_scoring_test_results.json", "w") as f:
            json.dump(sample_results, f, indent=2)

        print("\n📄 Sample results saved to 'transparent_scoring_test_results.json'")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
