"""
Transparent Scoring UI Components

This module provides Streamlit UI components for displaying transparent scoring
results with detailed breakdowns, making the scoring methodology auditable
and explainable to users.

Author: GretahAI ScriptWeaver
"""

import streamlit as st
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def display_transparent_scoring_breakdown(validation_results: Dict[str, Any]) -> None:
    """
    Display transparent scoring breakdown in Streamlit UI.

    Args:
        validation_results: Validation results containing transparent_scoring data
    """
    if not validation_results.get('transparent_scoring', {}).get('enabled', False):
        return

    transparent_data = validation_results['transparent_scoring']

    st.markdown("### 🔍 Transparent Quality Scoring")

    with st.expander("📊 Detailed Scoring Breakdown", expanded=True):
        # Overall score display
        total_score = transparent_data['total_score']
        max_score = transparent_data['max_possible_score']
        percentage = transparent_data['percentage']

        # Color-coded score display
        if percentage >= 80:
            score_color = "🟢"
            score_status = "Excellent"
            score_style = "color: #28a745;"
        elif percentage >= 60:
            score_color = "🟡"
            score_status = "Good"
            score_style = "color: #ffc107;"
        else:
            score_color = "🔴"
            score_status = "Needs Improvement"
            score_style = "color: #dc3545;"

        # Display overall score
        col1, col2, col3 = st.columns([2, 2, 2])
        with col1:
            st.markdown(f"""
            <div style="text-align: center; padding: 10px; border: 2px solid #ddd; border-radius: 5px;">
                <h3 style="{score_style} margin: 0;">Overall Score</h3>
                <h2 style="{score_style} margin: 5px 0;">{score_color} {total_score}/{max_score}</h2>
                <p style="margin: 0; color: #666;">{score_status}</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown(f"""
            <div style="text-align: center; padding: 10px; border: 2px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0; color: #333;">Percentage</h3>
                <h2 style="{score_style} margin: 5px 0;">{percentage:.1f}%</h2>
                <p style="margin: 0; color: #666;">of maximum possible</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            confidence = validation_results.get('confidence_rating', 'medium').title()
            ready = validation_results.get('ready_for_execution', True)
            ready_icon = "✅" if ready else "⚠️"
            ready_text = "Ready" if ready else "Needs Review"

            st.markdown(f"""
            <div style="text-align: center; padding: 10px; border: 2px solid #ddd; border-radius: 5px;">
                <h3 style="margin: 0; color: #333;">Status</h3>
                <h2 style="margin: 5px 0; color: #333;">{ready_icon} {ready_text}</h2>
                <p style="margin: 0; color: #666;">Confidence: {confidence}</p>
            </div>
            """, unsafe_allow_html=True)

        st.markdown("---")

        # Category breakdown
        st.markdown("#### 📋 Category Breakdown")

        breakdown = transparent_data.get('breakdown', [])

        # Create a table-like display for categories
        for category_data in breakdown:
            category = category_data['category']
            max_points = category_data['max_points']
            earned_points = category_data['earned_points']
            percentage = category_data['percentage']
            details = category_data['details']
            issues = category_data.get('issues', [])
            recommendations = category_data.get('recommendations', [])

            # Color coding for category performance
            if percentage >= 80:
                cat_color = "#28a745"
                cat_icon = "🟢"
            elif percentage >= 60:
                cat_color = "#ffc107"
                cat_icon = "🟡"
            else:
                cat_color = "#dc3545"
                cat_icon = "🔴"

            # Category header
            col1, col2, col3 = st.columns([3, 1, 1])
            with col1:
                st.markdown(f"**{cat_icon} {category}**")
            with col2:
                st.markdown(f"<span style='color: {cat_color}; font-weight: bold;'>{earned_points}/{max_points} pts</span>", unsafe_allow_html=True)
            with col3:
                st.markdown(f"<span style='color: {cat_color}; font-weight: bold;'>{percentage:.1f}%</span>", unsafe_allow_html=True)

            # Progress bar
            progress_value = earned_points / max_points if max_points > 0 else 0
            st.progress(progress_value)

            # Details
            if details:
                st.markdown(f"<small style='color: #666;'>{details}</small>", unsafe_allow_html=True)

            # Issues and recommendations in collapsible section
            if issues or recommendations:
                # Use container instead of expander to avoid nesting issues
                with st.container():
                    # Create a collapsible-style header
                    st.markdown(f"**📋 Details for {category}**")
                    if issues:
                        st.markdown("**Issues Found:**")
                        for issue in issues:
                            st.markdown(f"• ⚠️ {issue}")

                    if recommendations:
                        st.markdown("**Recommendations:**")
                        for rec in recommendations:
                            st.markdown(f"• 💡 {rec}")

            st.markdown("")  # Add spacing

        # Methodology explanation
        with st.expander("📖 Scoring Methodology", expanded=False):
            st.markdown("""
            **Transparent Scoring System**

            This scoring system uses scientifically-backed, rule-based criteria to evaluate test script quality.
            Unlike black box AI scoring, every point awarded or deducted is based on specific, auditable rules.

            **Scoring Categories & Weights:**
            - **Syntax (15 pts, 15%)**: Python syntax validity, imports, basic structure
            - **WebDriver Usage (15 pts, 15%)**: Proper browser fixture usage, method calls
            - **Locator Strategy (15 pts, 15%)**: Quality of element selectors, maintainability
            - **Wait Conditions (10 pts, 10%)**: Explicit waits, timeout handling
            - **Test Data Integration (10 pts, 10%)**: Proper use of manual input data
            - **Action Sequencing (10 pts, 10%)**: Logical flow of wait→interact→assert
            - **Error Handling (8 pts, 8%)**: Try/except blocks, error logging
            - **Assertions (12 pts, 12%)**: Meaningful test verifications
            - **Best Practices (3 pts, 3%)**: Pytest conventions, code quality
            - **Integration (2 pts, 2%)**: Compatibility with conftest.py

            **Total: 100 points maximum**

            **Quality Thresholds:**
            - 80-100: Excellent (🟢) - Production ready
            - 60-79: Good (🟡) - Minor improvements needed
            - 0-59: Needs Improvement (🔴) - Significant issues to address

            **Ready for Execution Criteria:**
            - Overall score ≥ 70%
            - Syntax score ≥ 50%
            - WebDriver Usage score ≥ 50%
            - Assertions score ≥ 50%
            """)


def display_scoring_comparison(
    ai_score: Optional[int] = None,
    transparent_score: Optional[int] = None,
    show_explanation: bool = True
) -> None:
    """
    Display comparison between AI and transparent scoring (if both available).

    Args:
        ai_score: AI-generated quality score
        transparent_score: Transparent scoring result
        show_explanation: Whether to show explanation of differences
    """
    if ai_score is None or transparent_score is None:
        return

    st.markdown("### 🔄 Scoring Method Comparison")

    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        st.metric("AI Score", f"{ai_score}/100", delta=None)

    with col2:
        st.metric("Transparent Score", f"{transparent_score}/100", delta=None)

    with col3:
        difference = transparent_score - ai_score
        delta_color = "normal" if abs(difference) <= 5 else "inverse"
        st.metric("Difference", f"{difference:+d}", delta=difference, delta_color=delta_color)

    if show_explanation and abs(difference) > 10:
        if difference > 10:
            st.info("🔍 **Transparent scoring is higher**: The rule-based system may be more lenient in certain areas or the AI may have identified issues not caught by the rules.")
        else:
            st.warning("🔍 **AI scoring is higher**: The AI may be more forgiving of certain issues, or the transparent rules may be stricter than the AI's assessment.")


def display_scoring_trends(validation_history: list) -> None:
    """
    Display scoring trends over time (if history is available).

    Args:
        validation_history: List of historical validation results
    """
    if not validation_history or len(validation_history) < 2:
        return

    st.markdown("### 📈 Quality Score Trends")

    # Extract scores from history
    transparent_scores = []
    ai_scores = []
    timestamps = []

    for result in validation_history:
        if result.get('transparent_scoring', {}).get('enabled'):
            transparent_scores.append(result['transparent_scoring']['total_score'])
        else:
            transparent_scores.append(None)

        ai_scores.append(result.get('quality_score'))
        timestamps.append(result.get('timestamp', len(timestamps)))

    # Create simple trend display
    if transparent_scores and any(score is not None for score in transparent_scores):
        latest_transparent = next((score for score in reversed(transparent_scores) if score is not None), None)
        previous_transparent = None
        for score in reversed(transparent_scores[:-1]):
            if score is not None:
                previous_transparent = score
                break

        if latest_transparent is not None and previous_transparent is not None:
            trend = latest_transparent - previous_transparent
            trend_icon = "📈" if trend > 0 else "📉" if trend < 0 else "➡️"
            trend_text = f"{trend:+d} points" if trend != 0 else "No change"

            st.markdown(f"**Quality Trend**: {trend_icon} {trend_text} from previous validation")


def create_scoring_summary_card(validation_results: Dict[str, Any]) -> None:
    """
    Create a compact summary card for scoring results.

    Args:
        validation_results: Validation results to summarize
    """
    if not validation_results.get('transparent_scoring', {}).get('enabled', False):
        # Fallback to traditional display
        quality_score = validation_results.get('quality_score', 0)
        ready = validation_results.get('ready_for_execution', True)

        if quality_score >= 80:
            color = "#28a745"
            icon = "🟢"
        elif quality_score >= 60:
            color = "#ffc107"
            icon = "🟡"
        else:
            color = "#dc3545"
            icon = "🔴"

        st.markdown(f"""
        <div style="padding: 10px; border: 2px solid {color}; border-radius: 5px; text-align: center;">
            <h4 style="margin: 0; color: {color};">{icon} Quality Score: {quality_score}/100</h4>
            <p style="margin: 5px 0; color: #666;">{'✅ Ready for execution' if ready else '⚠️ Needs review'}</p>
        </div>
        """, unsafe_allow_html=True)
        return

    # Transparent scoring summary
    transparent_data = validation_results['transparent_scoring']
    total_score = transparent_data['total_score']
    percentage = transparent_data['percentage']
    ready = validation_results.get('ready_for_execution', True)

    if percentage >= 80:
        color = "#28a745"
        icon = "🟢"
        status = "Excellent"
    elif percentage >= 60:
        color = "#ffc107"
        icon = "🟡"
        status = "Good"
    else:
        color = "#dc3545"
        icon = "🔴"
        status = "Needs Improvement"

    st.markdown(f"""
    <div style="padding: 10px; border: 2px solid {color}; border-radius: 5px; text-align: center;">
        <h4 style="margin: 0; color: {color};">{icon} Transparent Score: {total_score}/100 ({percentage:.1f}%)</h4>
        <p style="margin: 5px 0; color: #666;">{status} • {'✅ Ready for execution' if ready else '⚠️ Needs review'}</p>
    </div>
    """, unsafe_allow_html=True)
