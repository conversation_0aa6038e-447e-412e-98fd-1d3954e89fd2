"""
GretahAI ScriptWeaver - Test Script Generator Application

This module provides a Streamlit application for generating test scripts from Excel test cases.
The application follows a modular architecture with:

1. Centralized State Management:
   - Uses a StateManager dataclass to hold all session state
   - Provides helper methods for state mutations
   - Maintains consistent state across application stages

2. Modular UI Organization:
   - Splits UI logic into smaller stage-specific functions
   - Each stage function accepts and updates the state manager
   - Private helper functions handle repeated logic

3. Clean Separation of Concerns:
   - Pure helper functions extracted to separate modules
   - AI-related logic isolated in core/ai.py
   - UI components and constants in dedicated files

The application workflow:
1. Upload an Excel file containing test cases
2. Provide a website URL to extract UI elements
3. Use Google AI to convert test cases to automation-ready step tables
4. Detect and match UI elements to test steps
5. Configure test data for each step
6. Generate executable PyTest scripts
7. Execute the tests and capture results
"""

import os
import sys
import json
import logging
import streamlit as st
from datetime import datetime

# Configure logging
logger = logging.getLogger("ScriptWeaver.app")

# Set page config FIRST before any other Streamlit commands
st.set_page_config(
    page_title="GretahAI ScriptWeaver",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded",  # Always start with sidebar expanded
)

# --- Ensure GOOGLE_API_KEY is set from config.json before any LLM/genai usage ---
CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'config.json')
try:
    with open(CONFIG_PATH, 'r') as f:
        config = json.load(f)
        google_api_key = config.get('google_api_key')
        if (google_api_key):
            os.environ['GOOGLE_API_KEY'] = google_api_key
except Exception:
    pass

# Then add custom styling with st.markdown
st.markdown(
    """
    <style>
    /* --- Sidebar: purple background, white text --- */
    section[data-testid="stSidebar"] {
        background-color: #673AB7 !important;
    }
    section[data-testid="stSidebar"] *,
    section[data-testid="stSidebar"] .css-1lcbmhc {  /* catch headings, labels, etc. */
        color: #fff !important;
    }
    /* divider line in a slightly lighter purple */
    section[data-testid="stSidebar"] hr {
        border-color: #5E35B1 !important;
    }
    /* make metrics stand out */
    section[data-testid="stSidebar"] .stMetric,
    section[data-testid="stSidebar"] .stMetric .value {
        color: #fff !important;
    }
    </style>
    """,
    unsafe_allow_html=True,
)

# Add the parent directory to the path so we can import from parent modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

# Define the directory where app.py and other files are located
current_app_dir = os.path.dirname(os.path.abspath(__file__))
APP_CONFIG_FILE = os.path.join(current_app_dir, "config.json")
sys.path.insert(0, current_app_dir)  # Add to front to prioritize for dynamic imports

# Import stage functions from the new modular structure
from stages import (
    stage1_upload_excel,
    stage2_enter_website,
    stage3_convert_test_case,
    stage4_ui_detection_and_matching,
    stage5_test_data,
    stage6_generate_script,
    stage7_run_script,
    stage8_optimize_script,
    advance_to_next_step
)

# Helper Functions for Defensive Programming
def safe_len(value, default=0):
    """
    Safely get the length of a value, handling None values.

    Args:
        value: The value to get the length of (can be None, string, list, dict, etc.)
        default: The default value to return if value is None

    Returns:
        int: The length of the value, or default if value is None
    """
    return len(value) if value is not None else default

# UI Helper Functions
def _show_debug_info(state):
    """
    Display debug information about the application state.

    This function creates an expandable section with detailed state information
    and debug controls for developers.

    Args:
        state: The StateManager instance
    """
    with st.expander("Debug Information (for developers)", expanded=False):
        # Create tabs for different debug sections
        debug_tab1, debug_tab2, debug_tab3 = st.tabs(["State Variables", "Step Progression", "Workflow Hierarchy"])

        with debug_tab1:
            st.markdown("### State Variables")
            st.write("These variables control the 'Proceed to Next Step' button:")

            # Display key state variables as individual metrics for better visibility
            col1, col2 = st.columns(2)

            with col1:
                st.metric("current_step_index", str(state.current_step_index))
                st.metric("total_steps", str(state.total_steps))
                st.metric("step_ready_for_script", str(state.step_ready_for_script))
                st.metric("all_steps_done", str(state.all_steps_done))

            with col2:
                # Display additional state information if needed
                st.metric("Current Step", f"{state.current_step_index + 1} of {state.total_steps}")
                st.metric("Progress", f"{((state.current_step_index + 1) / max(1, state.total_steps) * 100):.1f}%")

                # Display completed steps if available
                if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
                    st.metric("Completed Steps", f"{len(state.completed_steps)} of {state.total_steps}")

            # Display additional information about the next step if available
            next_step_index = state.current_step_index + 1
            if next_step_index < state.total_steps and state.step_table_json:
                next_step = state.step_table_json[next_step_index]
                next_step_no = next_step.get('step_no', 'N/A')
                next_step_action = next_step.get('action', 'N/A')
                st.info(f"Next step info: Step {next_step_no} - {next_step_action}")

        with debug_tab2:
            # Display debug information about step progression
            st.markdown("### Step Progression Debug")

            # Show button click info if available
            if 'proceed_button_info' in st.session_state:
                st.write("Last 'Proceed to Next Step' button click:")
                st.json(st.session_state['proceed_button_info'])

            # Show state before and after advancement
            col1, col2 = st.columns(2)

            with col1:
                if 'debug_before_advance' in st.session_state:
                    st.write("State before last advance:")
                    st.json(st.session_state['debug_before_advance'])

            with col2:
                if 'debug_after_advance' in st.session_state:
                    st.write("State after last advance:")
                    st.json(st.session_state['debug_after_advance'])

            # Show step context if available
            if hasattr(state, 'step_context') and state.step_context:
                st.markdown("#### Step Context (saved state from previous steps)")
                # Create a toggle using a checkbox
                show_step_context = st.checkbox("Show step context data", value=False, key="show_step_context_debug")
                if show_step_context:
                    st.json(state.step_context)

        with debug_tab3:
            # Display information about the hierarchical workflow
            st.markdown("### Workflow Hierarchy Debug")

            # Suite level
            st.markdown("#### Suite Level (Stage 1)")
            if hasattr(state, 'test_cases') and state.test_cases:
                st.success(f"✅ Suite loaded with {len(state.test_cases)} test cases")
            else:
                st.warning("⚠️ No test cases loaded")

            # Test case level
            st.markdown("#### Test Case Level (Stage 3)")
            if hasattr(state, 'selected_test_case') and state.selected_test_case:
                st.success(f"✅ Test case selected: {state.selected_test_case.get('Test Case ID')}")

                # Show conversion status
                if hasattr(state, 'conversion_done') and state.conversion_done:
                    st.success("✅ Test case converted to step table")
                    if hasattr(state, 'step_table_json') and state.step_table_json:
                        st.info(f"Step table has {len(state.step_table_json)} steps")
                else:
                    st.warning("⚠️ Test case not converted")
            else:
                st.warning("⚠️ No test case selected")

            # Step level
            st.markdown("#### Step Level (Stages 4-7)")
            if hasattr(state, 'selected_step') and state.selected_step:
                st.success(f"✅ Step selected: Step {state.selected_step.get('Step No')}")

                # Show step progress
                step_status = []
                if hasattr(state, 'step_matches') and state.step_matches:
                    step_status.append("✅ UI Elements matched")
                else:
                    step_status.append("⚠️ UI Elements not matched")

                if hasattr(state, 'test_data') and state.test_data:
                    step_status.append("✅ Test data configured")
                elif hasattr(state, 'test_data_skipped') and state.test_data_skipped:
                    step_status.append("✅ Test data skipped")
                else:
                    step_status.append("⚠️ Test data not configured")

                if hasattr(state, 'generated_script_path') and state.generated_script_path:
                    step_status.append("✅ Script generated")
                else:
                    step_status.append("⚠️ Script not generated")

                for status in step_status:
                    st.write(status)
            else:
                st.warning("⚠️ No step selected")

        # Add debug actions in columns
        st.markdown("### Debug Actions")
        debug_col1, debug_col2 = st.columns(2)

        with debug_col1:
            # Add a button to reset the state
            if st.button("Reset State Variables", key="reset_state_debug"):
                # Ask for confirmation
                confirm = st.checkbox("Confirm Reset (this will clear all progress)", key="confirm_debug_reset")

                if confirm:
                    # Use the state manager's methods to reset state
                    state.update_step_progress(
                        step_ready_for_script=False,
                        script_just_generated=False
                    )

                    # Reset step-specific state
                    state.reset_step_state(confirm=True, reason="Debug reset requested by user")

                    st.success("State variables reset. Refresh the page to see the changes.")

                    # Force state update in session state
                    st.session_state['state'] = state
                    st.rerun()
                else:
                    st.warning("Please confirm the reset by checking the box above.")

            # Add a button to force step_ready_for_script flag
            if st.button("Set Ready for Script", key="set_ready_debug"):
                # Use the state manager's update method
                state.update_step_progress(step_ready_for_script=True)
                st.success("Set step_ready_for_script = True. Refresh the page to see the changes.")

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

        with debug_col2:
            # Add a direct manual advance function
            if st.button("Manually Advance to Next Step", key="manual_advance_debug"):
                # Call advance_to_next_step directly
                print("Debug panel: Manually advancing to next step")
                # advance_to_next_step() will call st.rerun() internally if successful
                advance_to_next_step()
                # The code below will only execute if advance_to_next_step() fails
                # and doesn't trigger a rerun
                st.error("Failed to advance to next step. See console output for details.")

            # Add a button to force automatic advancement
            if st.button("Force Auto-Advance After Test", key="force_auto_advance_debug"):
                # Set the necessary flags and call the automatic advancement logic
                state.step_ready_for_script = True

                # Check if there are more steps
                next_step_index = state.current_step_index + 1
                if next_step_index < state.total_steps:
                    # Get the next step information
                    next_step = state.step_table_json[next_step_index]
                    next_step_no = next_step.get('step_no', 'N/A')

                    # Store current step completion status
                    current_step_no = state.selected_step.get('Step No')
                    if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                        state.completed_steps = []
                    if current_step_no not in state.completed_steps:
                        state.completed_steps.append(current_step_no)

                    # Set up session state variables before calling advance_to_next_step
                    # since it will trigger a rerun if successful
                    st.session_state['stage_progression_message'] = f"✅ Forced advancement to Step {next_step_no}"

                    # Set a flag to force a refresh after advancement
                    from datetime import datetime
                    st.session_state['force_refresh_after_advance'] = {
                        'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                        'target_step': next_step_no,
                        'from_step': current_step_no
                    }

                    # Call advance_to_next_step to move to the next step
                    # This will call st.rerun() internally if successful
                    advance_to_next_step()

                    # The code below will only execute if advance_to_next_step() fails
                    # and doesn't trigger a rerun
                    st.error("Failed to force automatic advancement. See console output for details.")
                else:
                    st.warning("No more steps to advance to.")

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

            # Add a button to fix step selection issues
            if st.button("Fix Step Selection Issues", key="fix_step_selection_debug"):
                # This will force the UI to show the correct step based on current_step_index
                if hasattr(state, 'step_table_json') and state.step_table_json:
                    # Get the current step information
                    if 0 <= state.current_step_index < len(state.step_table_json):
                        current_step = state.step_table_json[state.current_step_index]
                        step_no = current_step.get('step_no', 'N/A')

                        # Find the corresponding original step
                        if hasattr(state, 'selected_test_case') and state.selected_test_case:
                            original_steps = state.selected_test_case.get('Steps', [])
                            selected_original_step = next(
                                (step for step in original_steps if str(step.get('Step No')) == str(step_no)),
                                None
                            )

                            if selected_original_step:
                                # Update the selected step
                                state.selected_step = selected_original_step
                                state.selected_step_table_entry = current_step

                                # Set a flag to force a refresh
                                from datetime import datetime
                                st.session_state['force_refresh_after_advance'] = {
                                    'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                                    'target_step': step_no,
                                    'from_step': 'unknown'
                                }

                                st.success(f"Fixed step selection to show Step {step_no} (index {state.current_step_index})")
                            else:
                                st.error(f"Could not find original step for step number {step_no}")
                        else:
                            st.error("No test case selected")
                    else:
                        st.error(f"Step index {state.current_step_index} is out of range (0-{len(state.step_table_json)-1})")
                else:
                    st.error("No step table available")

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

def _setup_page_style():
    """Set up the page style with custom CSS."""
    try:
        css_path = os.path.join(os.path.dirname(__file__), "style.css")
        with open(css_path) as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS file not found: {css_path}")
        # Fallback to inline CSS if file not found
        st.markdown("""
        <style>
            .main-header {
                font-size: 2.5rem;
                color: #673AB7;
                text-align: center;
                margin-bottom: 1rem;
            }
            .sub-header {
                font-size: 1.8rem;
                color: #8E24AA;
                margin-top: 2rem;
                margin-bottom: 1rem;
            }
            .stage-header, .step-header {
                font-size: 1.5rem;
                color: #5C6BC0;
                margin-top: 1.5rem;
                margin-bottom: 1rem;
                background-color: #F0E6FF;
                padding: 0.5rem;
                border-radius: 0.5rem;
            }
        </style>
        """, unsafe_allow_html=True)

def _display_workflow_summary_sidebar(state):
    """
    Display a streamlined summary of the current workflow state in the sidebar.

    This is the exclusive location for workflow progress indicators.
    Shows the hierarchical progression from suite to test case to step level,
    highlighting the current position in the workflow with minimal visual clutter.

    Args:
        state (StateManager): The application state manager instance
    """
    # Determine current application stage (1-8)
    current_app_stage = 1  # Default to Stage 1

    # Check for Stage 1 completion (test cases loaded)
    if hasattr(state, 'test_cases') and state.test_cases:
        current_app_stage = 3  # Completed Stage 1 and at Stage 3

        # Check for Stage 2 completion (website URL set)
        if hasattr(state, 'website_url') and state.website_url and state.website_url != "https://example.com":
            # Stage 2 is completed

            # Check for Stage 3 completion (test case selected and converted)
            if hasattr(state, 'selected_test_case') and state.selected_test_case:
                # At Stage 3
                current_app_stage = 3

                # Check if conversion is done (Stage 3 completed)
                if hasattr(state, 'conversion_done') and state.conversion_done and hasattr(state, 'step_table_json') and state.step_table_json:
                    current_app_stage = 4  # Stage 3 completed, at Stage 4

                    # Check for Stage 4 completion (step selected)
                    if hasattr(state, 'selected_step') and state.selected_step:
                        # Check for Stage 5 completion (UI elements matched)
                        if hasattr(state, 'step_matches') and state.step_matches:
                            current_app_stage = 5  # At Stage 5

                            # Check for Stage 6 completion (test data configured or skipped)
                            if (hasattr(state, 'test_data') and state.test_data) or (hasattr(state, 'test_data_skipped') and state.test_data_skipped):
                                current_app_stage = 6  # At Stage 6

                                # Check for Stage 7 completion (script generated)
                                if hasattr(state, 'generated_script_path') and state.generated_script_path:
                                    current_app_stage = 7  # At Stage 7

                                    # Check for Stage 8 (script optimization)
                                    if hasattr(state, 'optimization_in_progress') and state.optimization_in_progress:
                                        current_app_stage = 8  # At Stage 8 (in progress)
                                    elif hasattr(state, 'optimization_complete') and state.optimization_complete:
                                        current_app_stage = 8  # At Stage 8 (completed)
                                    elif (hasattr(state, 'combined_script_path') and state.combined_script_path and
                                          hasattr(state, 'all_steps_done') and state.all_steps_done):
                                        current_app_stage = 8  # At Stage 8 (ready for optimization)
    # If only website URL is set but no test cases, we're at Stage 2
    elif hasattr(state, 'website_url') and state.website_url and state.website_url != "https://example.com":
        current_app_stage = 2

    # Check completion status for each stage
    stage1_completed = hasattr(state, 'test_cases') and state.test_cases
    stage2_completed = hasattr(state, 'website_url') and state.website_url and state.website_url != "https://example.com"
    stage3_completed = (hasattr(state, 'selected_test_case') and state.selected_test_case and
                       hasattr(state, 'conversion_done') and state.conversion_done and
                       hasattr(state, 'step_table_json') and state.step_table_json)
    stage4_completed = stage3_completed and hasattr(state, 'selected_step') and state.selected_step
    stage5_completed = stage4_completed and hasattr(state, 'step_matches') and state.step_matches
    stage6_completed = stage5_completed and ((hasattr(state, 'test_data') and state.test_data) or
                                           (hasattr(state, 'test_data_skipped') and state.test_data_skipped))
    stage7_completed = stage6_completed and hasattr(state, 'generated_script_path') and state.generated_script_path
    stage8_completed = stage7_completed and hasattr(state, 'optimization_complete') and state.optimization_complete

    # Calculate completed stages count
    completed_stages = 0
    if stage1_completed:
        completed_stages += 1
    if stage2_completed:
        completed_stages += 1
    if stage3_completed:
        completed_stages += 1
    if stage4_completed:
        completed_stages += 1
    if stage5_completed:
        completed_stages += 1
    if stage6_completed:
        completed_stages += 1
    if stage7_completed:
        completed_stages += 1
    if stage8_completed:
        completed_stages += 1

    # Total number of stages
    total_stages = 8

    # Display workflow progress section
    st.markdown("### 🔄 Workflow Progress")

    # Create a visual progress bar for application stages
    stage_progress = completed_stages / total_stages
    st.progress(stage_progress)

    # Show current stage prominently but concisely
    st.markdown(f"**Current Phase:** {current_app_stage} of {total_stages} ({int(stage_progress * 100)}%)")

    # Display stage names with visual indicators in an expander
    with st.expander("View All Phases", expanded=False):
        stages = [
            "1️⃣ Upload Excel",
            "2️⃣ Enter Website URL",
            "3️⃣ Test Case Analysis",
            "4️⃣ UI Detection",
            "5️⃣ Test Data",
            "6️⃣ Generate Script",
            "7️⃣ Run Script",
            "8️⃣ Optimize Script"
        ]

        # Display stages with visual indicators
        for i, stage_name in enumerate(stages, 1):
            # Determine if the current stage is completed
            stage_completed = False
            if i == 1:
                stage_completed = stage1_completed
            elif i == 2:
                stage_completed = stage2_completed
            elif i == 3:
                stage_completed = stage3_completed
            elif i == 4:
                stage_completed = stage4_completed
            elif i == 5:
                stage_completed = stage5_completed
            elif i == 6:
                stage_completed = stage6_completed
            elif i == 7:
                stage_completed = stage7_completed
            elif i == 8:
                stage_completed = stage8_completed

            # Display the stage with appropriate styling
            if stage_completed:
                st.markdown(f"✅ ~~{stage_name}~~")  # Completed stage
            elif i == current_app_stage:
                st.markdown(f"**🔶 {stage_name}**")  # Current stage (highlighted)
            else:
                st.markdown(f"⬜ {stage_name}")  # Future stage

    st.markdown("---")

    # Current Status Section - Consolidated and streamlined
    with st.expander("Current Status", expanded=True):
        # Suite level information (Stage 1)
        if hasattr(state, 'test_cases') and state.test_cases:
            test_case_count = len(state.test_cases)
            st.success(f"✅ Test Suite: {test_case_count} test cases loaded")
        else:
            st.warning("⚠️ Test Suite: No test cases loaded")

        # Website URL information (Stage 2)
        if hasattr(state, 'website_url') and state.website_url and state.website_url != "https://example.com":
            st.success(f"✅ Website: {state.website_url}")
        else:
            st.warning("⚠️ Website: Not set")

        # Test case level information (Stage 3)
        if hasattr(state, 'selected_test_case') and state.selected_test_case:
            # Test case is selected
            tc_id = state.selected_test_case.get('Test Case ID', 'Unknown')
            tc_objective = state.selected_test_case.get('Test Case Objective', '')

            # Truncate objective if too long
            if len(tc_objective) > 50:
                tc_objective = tc_objective[:47] + "..."

            st.success(f"✅ Test Case: {tc_id}")

            # Show conversion status
            if hasattr(state, 'conversion_done') and state.conversion_done and hasattr(state, 'step_table_json') and state.step_table_json:
                # Stage 3 is completed
                step_count = len(state.step_table_json)
                st.success(f"✅ Conversion: {step_count} steps")
            else:
                # Stage 3 is not completed
                st.warning("⚠️ Conversion: Not completed")
        else:
            # No test case selected
            st.warning("⚠️ Test Case: None selected")

    # Step level information - Only show if a step is selected
    if hasattr(state, 'selected_step') and state.selected_step:
        with st.expander("Current Step Details", expanded=True):
            step_no = state.selected_step.get('Step No', 'Unknown')
            step_action = state.selected_step.get('Test Steps', '')

            # Truncate step action if too long
            if len(step_action) > 50:
                step_action = step_action[:47] + "..."

            # Create a more prominent step counter
            current_step = state.current_step_index + 1
            total_steps = state.total_steps

            # Display step counter with large text
            st.markdown(f"**Step {current_step} of {total_steps}**")
            st.markdown(f"**Step No:** {step_no}")

            if step_action:
                st.markdown(f"**Action:** {step_action}")

            # Show step status with concise visual indicators
            status_col1, status_col2 = st.columns(2)

            with status_col1:
                # UI Elements status
                if hasattr(state, 'step_matches') and state.step_matches:
                    st.success("✅ UI Elements")
                else:
                    st.warning("⚠️ UI Elements")

                # Script status
                if hasattr(state, 'generated_script_path') and state.generated_script_path:
                    st.success("✅ Script")
                else:
                    st.warning("⚠️ Script")

            with status_col2:
                # Test Data status
                if hasattr(state, 'test_data') and state.test_data:
                    st.success("✅ Test Data")
                elif hasattr(state, 'test_data_skipped') and state.test_data_skipped:
                    st.success("✅ Data Skipped")
                else:
                    st.warning("⚠️ Test Data")

                # Show step progress
                if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
                    completed_count = len(state.completed_steps)
                    completion_pct = int((completed_count / total_steps) * 100) if total_steps > 0 else 0
                    st.success(f"✅ Progress: {completion_pct}%")

def _setup_sidebar(state):
    """Set up the sidebar with workflow summary, branding and usage stats."""
    with st.sidebar:
        # Add ScriptWeaver branding
        st.image("https://cogniron.com/wp-content/uploads/2024/10/image-69.png", width=300)

        # Display workflow summary
        _display_workflow_summary_sidebar(state)

        st.markdown("---")
        st.markdown("### ☁️ Google AI Studio Usage")
        from datetime import timedelta
        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        one_day_ago = now - timedelta(days=1)
        # Ensure state lists exist
        if not hasattr(state, 'google_request_timestamps'):
            state.google_request_timestamps = []
        if not hasattr(state, 'google_token_usage'):
            state.google_token_usage = []
        # Filter timestamps for the relevant periods
        requests_last_minute = [ts for ts in state.google_request_timestamps if ts > one_minute_ago]
        requests_last_day = [ts for ts in state.google_request_timestamps if ts > one_day_ago]
        # Filter token usage for the last minute
        tokens_last_minute_data = [tup for tup in state.google_token_usage if tup[0] > one_minute_ago]
        tokens_last_minute_sum = sum(count for _, count in tokens_last_minute_data)
        # Calculate metrics
        current_rpm = len(requests_last_minute)
        current_tpm = tokens_last_minute_sum
        current_rpd = len(requests_last_day)
        with st.expander("Google AI Studio Usage (Live)", expanded=False):
            st.metric("TPM (Tokens/Minute)", f"{current_tpm:,}")
            st.metric("RPM (Requests/Minute)", f"{current_rpm}")
            st.metric("RPD (Requests/Day)", f"{current_rpd}")
            if st.button("Clear Usage Stats", key="clear_google_usage_stats_sidebar"):
                state.google_request_timestamps = []
                state.google_token_usage = []
                st.success("Usage stats cleared")
                st.rerun()

# Stage functions are now imported from stages.py

def run_app():
    """Main function to run the Streamlit application."""

    # --- Initialize State Manager ---
    from state_manager import StateManager
    StateManager().init_in_session(st)
    state = StateManager.get(st)

    # --- Page Setup ---
    # Page config is now set at the top of the file
    _setup_page_style()

    # Debug information about the state
    try:
        debug_mode = st.secrets.get("debug", False)
    except:
        debug_mode = False

    if debug_mode:
        print("=== State Manager Initialization ===")
        print(f"State: current_step_index = {state.current_step_index}")
        print(f"State: total_steps = {state.total_steps}")
        print(f"State: step_ready_for_script = {state.step_ready_for_script}")
        print(f"State: all_steps_done = {state.all_steps_done}")
        print("=== End State Manager Initialization ===")

    # Setup sidebar
    _setup_sidebar(state)

    # --- Header with ScriptWeaver branding ---
    st.markdown("<h1 class='main-header'>GretahAI ScriptWeaver</h1>", unsafe_allow_html=True)
    st.markdown("""
    <div class="feature-panel">
    Generate executable PyTest scripts from test cases.
    </div>
    """, unsafe_allow_html=True)

    # Create a container for stage progression messages
    stage_progression_container = st.container()

    # Check for stage progression messages in session state
    if 'stage_progression_message' in st.session_state:
        with stage_progression_container:
            st.success(st.session_state['stage_progression_message'])
            # Remove the message so it doesn't show up again
            del st.session_state['stage_progression_message']

    # ===== HIERARCHICAL STAGE EXECUTION =====

    # --- CHECK FOR STAGE 8 FIRST (HIGHEST PRIORITY) ---
    # Stage 8 should be independent of all other stage conditions and run at the top level
    should_run_stage8 = False
    stage8_reason = ""

    # Debug logging for Stage 8 conditions
    logger.info("=== STAGE 8 CONDITION CHECK (TOP LEVEL) ===")
    logger.info(f"transitioning_to_stage8 in session_state: {'transitioning_to_stage8' in st.session_state}")
    logger.info(f"optimization_in_progress: {getattr(state, 'optimization_in_progress', False)}")
    logger.info(f"optimization_complete: {getattr(state, 'optimization_complete', False)}")
    logger.info(f"combined_script_path: {getattr(state, 'combined_script_path', None)}")
    logger.info(f"all_steps_done: {getattr(state, 'all_steps_done', False)}")

    # Condition 1: Transitioning from Stage 7 to Stage 8 after completing all steps
    if 'transitioning_to_stage8' in st.session_state:
        should_run_stage8 = True
        stage8_reason = "transitioning from Stage 7"
        print("Transitioning from Stage 7 to Stage 8 after completing all steps")
        logger.info("Transitioning from Stage 7 to Stage 8 after completing all steps")

        # Verify that we have the combined script path in the state
        if hasattr(state, 'combined_script_path') and state.combined_script_path:
            logger.info(f"Combined script path is set in state: {state.combined_script_path}")
        else:
            logger.warning("Combined script path is not set in state before transitioning to Stage 8")
            if hasattr(state, 'previous_scripts') and state.previous_scripts:
                logger.info(f"Found {len(state.previous_scripts)} previous scripts, will attempt to create combined script in Stage 8")
            else:
                logger.warning("No previous scripts found in state, Stage 8 may fail")

        # Clear the flag now that we've processed it
        del st.session_state['transitioning_to_stage8']
        logger.info("Cleared transitioning_to_stage8 flag")

    # Condition 2: Optimization is in progress (check both state and session_state)
    elif ((hasattr(state, 'optimization_in_progress') and state.optimization_in_progress) or
          st.session_state.get('optimization_in_progress', False)):
        should_run_stage8 = True
        stage8_reason = "optimization in progress"
        print("Running Stage 8 because optimization is in progress")
        logger.info("Running Stage 8 because optimization is in progress")
        logger.info(f"State optimization_in_progress: {getattr(state, 'optimization_in_progress', 'NOT_SET')}")
        logger.info(f"Session state optimization_in_progress: {st.session_state.get('optimization_in_progress', 'NOT_SET')}")

        # Enhanced debugging for optimization detection
        logger.info("=" * 60)
        logger.info("STAGE 8 OPTIMIZATION DETECTION - ENHANCED DEBUGGING")
        logger.info("=" * 60)
        logger.info(f"State object ID: {id(state)}")
        logger.info(f"State object type: {type(state)}")
        logger.info(f"State has optimization_in_progress attr: {hasattr(state, 'optimization_in_progress')}")
        if hasattr(state, 'optimization_in_progress'):
            logger.info(f"State optimization_in_progress value: {state.optimization_in_progress}")
            logger.info(f"State optimization_in_progress type: {type(state.optimization_in_progress)}")
        logger.info(f"Session state keys: {list(st.session_state.keys())}")
        logger.info(f"Session state optimization_in_progress: {st.session_state.get('optimization_in_progress', 'NOT_FOUND')}")
        logger.info("=" * 60)

    # Condition 3: Optimization is complete but user hasn't chosen next action
    elif (hasattr(state, 'optimization_complete') and state.optimization_complete and
          hasattr(state, 'combined_script_path') and state.combined_script_path):
        should_run_stage8 = True
        stage8_reason = "optimization complete, showing results"
        print("Running Stage 8 because optimization is complete")
        logger.info("Running Stage 8 because optimization is complete")

    # Condition 4: All steps done and ready for optimization (MISSING CONDITION!)
    elif (hasattr(state, 'all_steps_done') and state.all_steps_done and
          not getattr(state, 'optimization_in_progress', False) and
          not getattr(state, 'optimization_complete', False)):
        # Check if we have a combined script, or if we can create one
        has_combined_script = (hasattr(state, 'combined_script_path') and state.combined_script_path and
                              hasattr(state, 'combined_script_content') and state.combined_script_content)

        has_previous_scripts = (hasattr(state, 'previous_scripts') and state.previous_scripts)

        if has_combined_script or has_previous_scripts:
            should_run_stage8 = True
            stage8_reason = "all steps done, ready for optimization"
            print("Running Stage 8 because all steps are done and ready for optimization")
            logger.info("Running Stage 8 because all steps are done and ready for optimization")
            logger.info(f"all_steps_done: {state.all_steps_done}")
            logger.info(f"has_combined_script: {has_combined_script}")
            logger.info(f"has_previous_scripts: {has_previous_scripts}")
            logger.info(f"combined_script_path: {getattr(state, 'combined_script_path', 'NOT_SET')}")
            # Safe length check for combined_script_content (handles None values)
            combined_content = getattr(state, 'combined_script_content', '')
            logger.info(f"combined_script_content length: {safe_len(combined_content)}")
            logger.info(f"previous_scripts count: {len(getattr(state, 'previous_scripts', {}))}")
            logger.info(f"optimization_in_progress: {getattr(state, 'optimization_in_progress', False)}")
            logger.info(f"optimization_complete: {getattr(state, 'optimization_complete', False)}")
        else:
            logger.warning("All steps done but no combined script or previous scripts available for optimization")
            logger.warning(f"combined_script_path: {getattr(state, 'combined_script_path', 'NOT_SET')}")
            # Safe length check for combined_script_content (handles None values)
            combined_content = getattr(state, 'combined_script_content', '')
            logger.warning(f"combined_script_content: {safe_len(combined_content)} chars")
            logger.warning(f"previous_scripts: {getattr(state, 'previous_scripts', 'NOT_SET')}")

    # Run Stage 8 if any of the conditions are met
    if should_run_stage8:
        logger.info(f"Calling stage8_optimize_script function (reason: {stage8_reason})")
        stage8_optimize_script(state)
        return  # Exit early to prevent other stages from running

    # --- SUITE LEVEL (Stage 1-2) ---
    # Always run the first two stages unconditionally
    stage1_upload_excel(state)
    stage2_enter_website(state)

    # --- TEST CASE LEVEL (Stage 3) ---
    # Only run Stage 3 if Stage 1 is completed (test cases are loaded)
    if hasattr(state, 'test_cases') and state.test_cases:
        # Log the transition to test case level
        print("Transitioning to TEST CASE LEVEL (Stage 3)")

        # Check if we're transitioning from Stage 7 to Stage 3 after completing all steps
        transitioning_to_stage3 = False
        if 'transitioning_to_stage3' in st.session_state:
            transitioning_to_stage3 = True
            print("Transitioning from Stage 7 to Stage 3 after completing all steps")

            # Clear the flag now that we've processed it
            del st.session_state['transitioning_to_stage3']

        # Run Stage 3 (Test Case Analysis and Conversion)
        stage3_convert_test_case(state)

        # Check if we're transitioning from Stage 8 to Stage 3 after completing script optimization
        transitioning_from_stage8 = False
        if 'transitioning_from_stage8' in st.session_state:
            transitioning_from_stage8 = True
            print("Transitioning from Stage 8 to Stage 3 after completing script optimization")

            # Clear the flag now that we've processed it
            del st.session_state['transitioning_from_stage8']

        # If we're transitioning from Stage 7 or Stage 8 to Stage 3, stop here to let the user select a new test case
        if transitioning_to_stage3 or transitioning_from_stage8:
            print("Stopping at Stage 3 after transitioning from Stage 7 or Stage 8")
            return

        # --- STEP LEVEL (Stages 4-7) ---
        # Only run Stage 4 if Stage 3 is completed (test case is selected and converted)
        if (hasattr(state, 'selected_test_case') and state.selected_test_case and
            hasattr(state, 'conversion_done') and state.conversion_done and
            hasattr(state, 'step_table_json') and state.step_table_json):

            # Log the transition to step level
            print("Transitioning to STEP LEVEL (Stages 4-7)")

            # Check if we're coming back from Stage 7 with a new step
            coming_from_stage7 = False
            if 'coming_from_stage7' in st.session_state:
                coming_from_stage7 = True
                print(f"Coming back from Stage 7 to Stage 4 with step index {state.current_step_index}")

                # Ensure the state is properly updated with the new step
                if 'force_refresh_after_advance' in st.session_state:
                    target_step = st.session_state['force_refresh_after_advance'].get('target_step')
                    print(f"Target step from Stage 7: {target_step}")

                    # Find the step in the step table
                    if hasattr(state, 'step_table_json') and state.step_table_json:
                        matching_step = next(
                            (step for step in state.step_table_json if str(step.get('step_no')) == str(target_step)),
                            None
                        )

                        if matching_step:
                            # Find the index of this step
                            try:
                                target_index = [str(step.get('step_no')) for step in state.step_table_json].index(str(target_step))
                                print(f"Found target step at index {target_index}")

                                # Update the current step index
                                if state.current_step_index != target_index:
                                    print(f"Updating current_step_index from {state.current_step_index} to {target_index}")
                                    state.current_step_index = target_index

                                    # Find the corresponding original step
                                    original_steps = state.selected_test_case.get('Steps', [])
                                    selected_original_step = next(
                                        (step for step in original_steps if str(step.get('Step No')) == str(target_step)),
                                        None
                                    )

                                    if selected_original_step:
                                        # Update the selected step
                                        state.selected_step = selected_original_step
                                        state.selected_step_table_entry = matching_step
                                        print(f"Updated selected step to Step {target_step}")
                            except ValueError:
                                print(f"Could not find index for step {target_step}")
                        else:
                            print(f"Could not find matching step for step {target_step}")

            # Run Stage 4 (UI Detection and Test Case Step Selection)
            stage4_ui_detection_and_matching(state)

            # If we're coming from Stage 7, stop here to let the user interact with Stage 4
            # This prevents automatically proceeding to Stages 5-7 with the new step
            if coming_from_stage7:
                print("Stopping at Stage 4 after returning from Stage 7")
                # Now that we've processed the flag, clear it
                if 'coming_from_stage7' in st.session_state:
                    del st.session_state['coming_from_stage7']
                return

            # Only run subsequent stages if we have a selected step
            if hasattr(state, 'selected_step') and state.selected_step:
                # Check if test data is already skipped
                test_data_skipped = getattr(state, 'test_data_skipped', False)

                # Log current state for debugging
                print(f"Current state: step_index={state.current_step_index}, total_steps={state.total_steps}, ready_for_script={state.step_ready_for_script}")

                # Run Stage 5 (Test Data Configuration)
                stage5_test_data(state)

                # Only run Stage 6 if we have element matches or test data is skipped
                if (hasattr(state, 'step_matches') and state.step_matches) or test_data_skipped:
                    # Run Stage 6 (Script Generation)
                    stage6_generate_script(state)

                    # Only run Stage 7 if we have a generated script
                    if hasattr(state, 'generated_script_path') and state.generated_script_path:
                        # Run Stage 7 (Script Execution)
                        stage7_run_script(state)

    # Log the current state for debugging
    print(f"End of run_app: step_index={state.current_step_index}, total_steps={state.total_steps}, ready_for_script={state.step_ready_for_script}")

    # Commercial Footer with comprehensive licensing information
    st.markdown("---")
    st.markdown("""
    <div class="scriptweaver-footer">
        <div style="margin-bottom: 10px;">
            <strong>GretahAI ScriptWeaver</strong> v2.1.0 | © 2025 Cogniron. All Rights Reserved.
        </div>
        <div style="font-size: 0.8rem; color: rgba(255,255,255,0.8); margin-bottom: 8px;">
            <strong>PROPRIETARY COMMERCIAL SOFTWARE</strong> - This software is proprietary and confidential.
        </div>
        <div style="font-size: 0.8rem; color: rgba(255,255,255,0.8); margin-bottom: 8px;">
            <strong>Commercial Licensing:</strong> Valid commercial license required for all use.
            Unauthorized copying, distribution, or use is strictly prohibited.
        </div>
        <div style="font-size: 0.8rem; color: rgba(255,255,255,0.8);">
            <strong>Enterprise Support:</strong> <a href="mailto:<EMAIL>" style="color: #B39DDB;"><EMAIL></a> |
            <strong>Website:</strong> <a href="https://cogniron.com" target="_blank" style="color: #B39DDB;">cogniron.com</a>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Add debug information at the bottom
    _show_debug_info(state)

if __name__ == "__main__":
    run_app()
