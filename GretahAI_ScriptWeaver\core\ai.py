"""
Module for AI integration.
Provides functions for interacting with LLMs and manages prompt templates.
Includes comprehensive logging of AI interactions with detailed metrics and function call tracing.
"""

import os
import json
import time
import re
import uuid
import inspect
from typing import Optional, Dict, Any
import google.generativeai as genai
from datetime import datetime

# Import helper functions and classes from ai_helpers.py
from .ai_helpers import (
    # Core utility functions
    capture_call_stack, log_metrics_to_csv, update_google_usage, markdown_table_to_json, json_to_markdown_table,
    get_by_pos, safe_int, load_config,extract_json_from_response, error_handler,
    extract_markdown_from_response, extract_token_counts, format_call_stack_section, format_metadata_section,
    # Classes (using singleton pattern)
    LoggingManager,
    RequestTracker,
    TokenUsageTracker
)

# Import the modularized prompt builder
from .prompt_builder import generate_test_script_prompt, generate_enhanced_test_script_prompt

# Initialize the logging manager
logging_manager = LoggingManager.get_instance()
logger = logging_manager.logger

# Initialize the request tracker
request_tracker = RequestTracker.get_instance()

# Initialize the token usage tracker
token_tracker = TokenUsageTracker.get_instance()

def log_ai_interaction(function_name, prompt, response, model_name="gemini-1.5-flash",
                      request_id=None, parent_request_id=None, context=None,
                      input_tokens=None, output_tokens=None, latency_ms=None,
                      error=None, category="general", related_request_ids=None,
                      capture_stack=True, is_prompt_generation=False):
    """
    Log AI interaction to a file with comprehensive metadata and function call tracing.

    Args:
        function_name (str): Name of the function making the API call
        prompt (str): The prompt sent to the AI
        response (str): The response received from the AI
        model_name (str, optional): The AI model used. Defaults to "gemini-1.5-flash"
        request_id (str, optional): Unique ID for this request. Generated if not provided.
        parent_request_id (str, optional): ID of a parent request if this is a follow-up.
        context (dict, optional): Additional context information about the request.
        input_tokens (int, optional): Number of input tokens used.
        output_tokens (int, optional): Number of output tokens generated.
        latency_ms (float, optional): Request latency in milliseconds.
        error (Exception, optional): Exception if the request failed.
        category (str, optional): Category of the interaction (e.g., "script_generation", "step_table").
        related_request_ids (list, optional): List of related request IDs for cross-referencing.
        capture_stack (bool, optional): Whether to capture and include call stack information.
        is_prompt_generation (bool, optional): Whether this is a prompt generation log (affects how call stack is captured).

    Returns:
        tuple: (filepath, request_id) - Path to the log file and the request ID
    """
    try:
        # Generate a request ID if not provided
        if not request_id:
            request_id = str(uuid.uuid4())

        # Get the log file path
        filepath = logging_manager.get_log_path(category, function_name, request_id, error is not None)

        # Calculate token counts if not provided
        if input_tokens is None and prompt:
            input_tokens = len(prompt.split())

        if output_tokens is None and response and isinstance(response, str):
            output_tokens = len(response.split())

        # Capture call stack information if requested
        call_stack_info = None
        if capture_stack:
            # Determine if this is a prompt generation function
            is_prompt_gen = is_prompt_generation or any(name in function_name for name in [
                'prompt', 'generate_test_script_prompt', 'generate_test_data_prompt',
                'convert_test_case_to_step_table', 'merge_scripts_with_ai'
            ])

            # Capture call stack with appropriate settings
            call_stack_info = capture_call_stack(
                skip_frames=2,  # Skip this function and its caller
                is_prompt_generation=is_prompt_gen
            )

        # Store context information for this request
        request_data = {
            'function_name': function_name,
            'timestamp': datetime.now(),
            'model_name': model_name,
            'parent_request_id': parent_request_id,
            'related_request_ids': related_request_ids or [],
            'context': context or {},
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'latency_ms': latency_ms,
            'category': category,
            'error': str(error) if error else None,
            'filepath': filepath,
            'call_stack': call_stack_info
        }

        # Add the request to the tracker
        request_tracker.add_request(request_id, request_data)

        # Format the metadata section
        content = format_metadata_section(
            request_id, function_name, category, model_name,
            parent_request_id, related_request_ids or [],
            input_tokens, output_tokens, latency_ms, error, context
        )

        # Add function call trace if available
        if call_stack_info:
            content += format_call_stack_section(call_stack_info, function_name, is_prompt_generation)

        # Add prompt and response sections
        content += f"""
==============================================================================
PROMPT:
==============================================================================
{prompt}

==============================================================================
RESPONSE:
==============================================================================
{response}
"""

        # Write to the file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        # Log metrics to a separate CSV file for analysis
        log_metrics_to_csv(request_id, function_name, category, model_name,
                          input_tokens, output_tokens, latency_ms, error is not None)

        # Update parent request with this child request ID if applicable
        if parent_request_id:
            request_tracker.link_requests(parent_request_id, request_id)
            request_tracker.update_parent_log(parent_request_id, request_id, filepath)

        logger.info(f"AI interaction logged to {filepath} [Request ID: {request_id}]")
        return filepath, request_id
    except Exception as e:
        logger.error(f"Error logging AI interaction: {e}")
        return None, request_id

@error_handler
def initialize_ai_client(api_key=None):
    """
    Initialize the Google AI client.

    Args:
        api_key (str, optional): API key for Google AI. If None, load from config

    Returns:
        bool: True if successful, False otherwise
    """
    # If API key is provided, use it directly
    if not api_key:
        # Try to load from config.json first
        try:
            config = load_config()
            api_key = config.get('google_api_key')
        except Exception as e:
            logger.warning(f"Error loading from config.json: {e}")

        # If still no API key, try scriptweaver_config.json
        if not api_key:
            try:
                # Get the directory where this file is located
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                config_path = os.path.join(parent_dir, "scriptweaver_config.json")

                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                        api_key = config.get('google_api_key')
                        logger.info(f"Loaded API key from scriptweaver_config.json")
            except Exception as e:
                logger.warning(f"Error loading from scriptweaver_config.json: {e}")

    if not api_key:
        logger.warning("Google API key not found in any config file")
        return False

    # Configure the Google AI client
    genai.configure(api_key=api_key)
    logger.info("Google AI client initialized successfully")

    # Add to usage tracking if needed
    if 'google_token_usage' not in globals():
        globals()['google_token_usage'] = []

    return True

@error_handler
def generate_llm_response(prompt=None, model_name=None, api_key=None,
                     context=None, parent_request_id=None, category="general",
                     related_request_ids=None, system_prompt=None, user_prompt=None,
                     function_name="generate_llm_response"):
    """
    Generate a response from the LLM based on the provided prompt with comprehensive logging.

    Args:
        prompt (str, optional): The prompt to send to the LLM (legacy parameter)
        model_name (str, optional): The LLM model to use. Defaults to "gemini-1.5-flash"
        api_key (str, optional): API key for Google AI. If None, use initialized client
        context (dict, optional): Additional context information about the request
        parent_request_id (str, optional): ID of a parent request if this is a follow-up
        category (str, optional): Category of the interaction (e.g., "script_generation")
        related_request_ids (list, optional): List of related request IDs for cross-referencing
        system_prompt (str, optional): System prompt for the LLM (for newer models)
        user_prompt (str, optional): User prompt for the LLM (for newer models)
        function_name (str, optional): Name of the function calling this method (for logging)

    Returns:
        str: The LLM response
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    input_tokens = None
    output_tokens = None

    # Handle different prompt formats (legacy prompt or system/user prompts)
    if prompt is None and user_prompt is not None:
        # Using the newer system/user prompt format
        prompt = user_prompt  # For logging purposes

        # Check if the model supports system prompts
        if "gemini-2.0" in model_name:
            # Gemini 2.0 models don't support system prompts, so combine them
            if system_prompt:
                # Combine system and user prompts for models that don't support system prompts
                combined_prompt = f"System instructions: {system_prompt}\n\nUser request: {user_prompt}"
                actual_prompt = combined_prompt
            else:
                actual_prompt = user_prompt
        else:
            # For models that support system/user roles
            actual_prompt = {"role": "user", "parts": [user_prompt]}
            if system_prompt:
                actual_prompt = [{"role": "system", "parts": [system_prompt]}, actual_prompt]
    else:
        # Using the legacy single prompt format
        actual_prompt = prompt

    # Add caller information to context
    caller_info = {}
    try:
        frame = inspect.currentframe().f_back
        if frame:
            caller_info = {
                'caller_file': frame.f_code.co_filename,
                'caller_function': frame.f_code.co_name,
                'caller_lineno': frame.f_lineno
            }
    except Exception:
        pass

    if context is None:
        context = {}
    context['caller_info'] = caller_info

    # If model_name is not provided, try to load it from config
    if not model_name:
        try:
            # Try scriptweaver_config.json first
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            config_path = os.path.join(parent_dir, "scriptweaver_config.json")

            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    model_name = config.get('model_name')
                    logger.info(f"Loaded model_name from scriptweaver_config.json: {model_name}")
        except Exception as e:
            logger.warning(f"Error loading model_name from scriptweaver_config.json: {e}")

        # If still no model_name, try the regular config
        if not model_name:
            try:
                config = load_config()
                model_name = config.get('model_name')
                logger.info(f"Loaded model_name from config.json: {model_name}")
            except Exception as e:
                logger.warning(f"Error loading model_name from config.json: {e}")

        # If still no model_name, use default
        if not model_name:
            model_name = "gemini-2.0-flash"
            logger.info(f"Using default model_name: {model_name}")

    # Always explicitly configure the client with the API key (from param or env)
    if not api_key:
        api_key = os.environ.get("GOOGLE_API_KEY")
    if not api_key:
        # Try to initialize the client to get the API key
        if not initialize_ai_client():
            raise ValueError("No Google API key provided or found in any config file.")
        # Try again with environment variable that might have been set by initialize_ai_client
        api_key = os.environ.get("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("No Google API key provided or found in any config file.")
    genai.configure(api_key=api_key)

    # Configure the model
    model = genai.GenerativeModel(model_name)

    # Generate the response
    logger.info(f"Sending request to {model_name} [Request ID: {request_id}]")
    response = model.generate_content(actual_prompt)

    # Calculate latency
    end_time = time.time()
    latency_ms = (end_time - start_time) * 1000

    # Extract token counts
    input_tokens, output_tokens = extract_token_counts(response, prompt)

    # Track token usage
    token_tracker.add_usage(input_tokens)

    # Update Google usage stats in session state
    update_google_usage(input_tokens)

    # Prepare context information
    request_context = context.copy()
    request_context.update({
        'model': model_name,
        'timestamp': datetime.now().isoformat(),
        'latency_ms': latency_ms,
        'input_tokens': input_tokens,
        'output_tokens': output_tokens,
        'total_tokens': input_tokens + output_tokens if input_tokens and output_tokens else None,
        'has_system_prompt': system_prompt is not None
    })

    # Determine what to log as the prompt
    log_prompt = prompt
    if system_prompt and user_prompt:
        log_prompt = f"SYSTEM PROMPT:\n{system_prompt}\n\nUSER PROMPT:\n{user_prompt}"

    # Log the interaction with enhanced metadata and call stack
    log_ai_interaction(
        function_name=function_name,
        prompt=log_prompt,
        response=response.text,
        model_name=model_name,
        request_id=request_id,
        parent_request_id=parent_request_id,
        related_request_ids=related_request_ids,
        context=request_context,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        latency_ms=latency_ms,
        category=category,
        capture_stack=True
    )

    logger.info(f"LLM response generated successfully in {latency_ms:.2f}ms [Request ID: {request_id}]")

    # Return the text
    return response.text

@error_handler
def convert_test_case_to_step_table(test_case_json, api_key=None):
    """
    Convert a test case JSON to an automation-ready step table using Google AI.

    Args:
        test_case_json (dict): The test case JSON to convert
        api_key (str, optional): API key for Google AI. If None, use initialized client

    Returns:
        tuple: (markdown_table, json_table) where:
            - markdown_table (str): The automation-ready step table in markdown format
            - json_table (list): The step table as a list of dictionaries
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Validate input
    if not test_case_json or not isinstance(test_case_json, dict):
        logger.warning(f"Invalid test case JSON provided [Request ID: {request_id}]")
        return "Error: Invalid test case JSON provided.", []

    # Create the prompt with the test case JSON
    json_input = json.dumps(test_case_json, indent=2)
    prompt = """
Role: You are a senior QA-automation engineer.

Convert the test-case JSON that follows into an **automation-ready step table**.

____________________________________
INPUT JSON
%s
____________________________________

OUTPUT FORMAT
I need the data in JSON format first, followed by a markdown table representation:

1. First, provide a JSON array of objects, where each object represents a step with these exact keys (in this order):
   "step_no", "step_type", "action", "locator_strategy", "locator", "test_data_param", "expected_result", "assertion_type", "condition", "timeout"

2. Second, provide the same data as a Markdown table with **one row per step** and these exact columns (in this order):
   | Step No | Step Type | Action | Locator Strategy | Locator | Test Data Param | Expected Result | Assertion Type | Condition | Timeout (s) |

**Instructions**

1. **step_type** – One of:
      - `setup` (e.g. API or DB calls to prepare state)
      - `ui` (interact with the user interface)
      - `api` (direct HTTP/API calls)
      - `data` (data setup or teardown)
      - `assertion` (verifications without UI interactions)
      - `teardown` (cleanup steps)
2. **action** – Convert each test step into an imperative verb:
      - UI: `navigate`, `click`, `type`, `select`, `upload_file`, `wait_for_element`, etc.
      - API: `api_get`, `api_post`, `api_delete`, etc.
      - Data: `db_insert`, `db_delete`, etc.
3. **locator_strategy** – For UI steps choose `css`, `xpath`, `id`, `name`, `aria`, or leave blank for non-UI steps.
4. **locator** – Draft a plausible selector (e.g. `#login-button`, `//input[@name="q"]`) or leave blank if not applicable.
5. **test_data_param** – Placeholder inputs in `{{double_curly}}` form, e.g. `{{username}}`. For loops, set `action` to `for_each` and this to the collection variable.
6. **expected_result** – Succinct description (≤ 10 words), e.g. `dashboard_page`, `201_created`.
7. **assertion_type** – Oracle keywords such as `url_equals`, `element_visible`, `text_equals`, `status_code`, `no_error`.
8. **condition** – If the step runs conditionally, put a boolean expression (e.g. `element_visible("#error")`); otherwise, an empty string.
9. **timeout** – Maximum wait time in seconds (default UI = 10, API = 30).
10. Keep the original **step_no** and preserve step order.
11. **Do not** add any commentary between the JSON and the Markdown outputs.

Format your response exactly like this:

```json
[
  {
    "step_no": "1",
    "step_type": "setup",
    "action": "api_post",
    "locator_strategy": "",
    "locator": "",
    "test_data_param": "{api_base_url}/create-user",
    "expected_result": "201_created",
    "assertion_type": "status_code",
    "condition": "",
    "timeout": 30
  }
]
```

```markdown
| Step No | Step Type | Action | Locator Strategy | Locator | Test Data Param | Expected Result | Assertion Type | Condition | Timeout (s) |
|---------|-----------|--------|------------------|---------|-----------------|-----------------|----------------|-----------|-------------|
| 1 | setup | api_post | | | {api_base_url}/create-user | 201_created | status_code | | 30 |
```

IMPORTANT: The JSON output must come first, followed by the markdown table. Both outputs must be immediately consumable by a PyTest/Selenium generator.
"""%json_input

    # Create context information for this request
    context = {
        'test_case_id': test_case_json.get('id', 'unknown'),
        'test_case_name': test_case_json.get('name', 'unknown'),
        'step_count': len(test_case_json.get('steps', [])),
        'operation': 'convert_to_step_table'
    }

    # Log the prompt for step table generation separately with enhanced prompt generation tracing
    _, prompt_request_id = log_ai_interaction(
        function_name="convert_test_case_to_step_table_prompt",
        prompt=prompt,
        response="See generate_llm_response log for the response",
        model_name="gemini-1.5-flash",
        capture_stack=True,
        is_prompt_generation=True,  # Enable prompt generation tracing
        context=context
    )

    # Use generate_llm_response with enhanced logging and call stack tracing
    logger.info(f"Converting test case to step table [Request ID: {request_id}]")
    response_text = generate_llm_response(
        prompt=prompt,
        model_name="gemini-1.5-flash",
        api_key=api_key,
        context=context,
        category="step_table_generation",
        parent_request_id=None,
        related_request_ids=[prompt_request_id]  # Link to the prompt log
    )

    # Calculate processing time for parsing
    parsing_start_time = time.time()

    # Extract JSON and markdown from the response
    json_table = extract_json_from_response(response_text, request_id)
    markdown_table = extract_markdown_from_response(response_text, request_id)

    # If we have JSON but no markdown table, generate the markdown table from JSON
    if json_table and not markdown_table:
        markdown_table = json_to_markdown_table(json_table)
        logger.info(f"Generated markdown table from JSON [Request ID: {request_id}]")

    # If we have markdown table but no JSON, generate JSON from the markdown table
    if not json_table and markdown_table:
        json_table = markdown_table_to_json(markdown_table)
        logger.info(f"Created JSON from markdown table with {len(json_table)} entries [Request ID: {request_id}]")

    # Calculate parsing time
    parsing_end_time = time.time()
    parsing_time_ms = (parsing_end_time - parsing_start_time) * 1000

    # Calculate total processing time
    total_time_ms = (parsing_end_time - start_time) * 1000

    # Log the parsing results with detailed metrics
    parsing_context = {
        'test_case_id': test_case_json.get('id', 'unknown'),
        'json_steps_found': len(json_table),
        'markdown_table_found': bool(markdown_table),
        'parsing_time_ms': parsing_time_ms,
        'total_processing_time_ms': total_time_ms
    }

    # Log the parsing results
    log_ai_interaction(
        function_name="parse_step_table_results",
        prompt="",  # No prompt for this log entry
        response=f"JSON Steps: {len(json_table)}\nMarkdown Table: {'Found' if markdown_table else 'Not Found'}\nParsing Time: {parsing_time_ms:.2f}ms",
        model_name="N/A",
        request_id=str(uuid.uuid4()),
        parent_request_id=request_id,  # Link to the original request
        context=parsing_context,
        input_tokens=0,
        output_tokens=0,
        latency_ms=parsing_time_ms,
        category="step_table_parsing"
    )

    logger.info(f"Step table conversion completed in {total_time_ms:.2f}ms [Request ID: {request_id}]")
    return markdown_table, json_table

def generate_test_data_prompt(step_data, ui_elements, step_table_entry=None):
    """
    Generate a prompt for test data generation.

    Args:
        step_data (dict): Test step data with action and expected result
        ui_elements (list): List of UI elements relevant to the test step
        step_table_entry (dict, optional): Step table entry with structured data

    Returns:
        str: The prompt for test data generation
    """
    logger.info("=== ENTERING generate_test_data_prompt ===")
    logger.info(f"Input step_data: {step_data}")
    logger.info(f"Input ui_elements: {type(ui_elements)} with {len(ui_elements) if isinstance(ui_elements, list) else 'N/A'} elements")
    logger.info(f"Input step_table_entry: {step_table_entry}")

    try:
        # Prepare the prompt for AI
        logger.info("Preparing prompt for AI")
        step_table_info = ""
        if step_table_entry and isinstance(step_table_entry, dict):
            logger.info("Building step table info for prompt")
            step_table_info = f"""
            Step Table Information:
            - Step Type: {step_table_entry.get('step_type', 'N/A')}
            - Action: {step_table_entry.get('action', 'N/A')}
            - Locator Strategy: {step_table_entry.get('locator_strategy', 'N/A')}
            - Locator: {step_table_entry.get('locator', 'N/A')}
            - Test Data Parameter: {step_table_entry.get('test_data_param', 'N/A')}
            - Expected Result: {step_table_entry.get('expected_result', 'N/A')}
            - Assertion Type: {step_table_entry.get('assertion_type', 'N/A')}
            """
            logger.info("Successfully built step table info")
        else:
            logger.info("No step table entry provided or it's not a dictionary")

        logger.info("Building main prompt")
        # Safely serialize UI elements to JSON
        try:
            ui_elements_json = json.dumps(ui_elements, indent=2)
            logger.info(f"Successfully serialized UI elements to JSON ({len(ui_elements_json)} chars)")
        except Exception as json_err:
            logger.error(f"Error serializing UI elements to JSON: {json_err}")
            # Create a simplified version that can be serialized
            simplified_elements = []
            if isinstance(ui_elements, list):
                for elem in ui_elements:
                    if isinstance(elem, dict):
                        simplified_elem = {
                            "name": elem.get("name", "Unknown"),
                            "type": elem.get("type", "Unknown"),
                            "attributes": {
                                "id": elem.get("attributes", {}).get("id", ""),
                                "tag": elem.get("attributes", {}).get("tag", "")
                            }
                        }
                        simplified_elements.append(simplified_elem)
            ui_elements_json = json.dumps(simplified_elements, indent=2)
            logger.info(f"Created simplified UI elements JSON ({len(ui_elements_json)} chars)")

        prompt = f"""
        I need to generate realistic and contextually appropriate test data for the following test step:
        - Action: {step_data.get('action', '')}
        - Expected Result: {step_data.get('expected', '')}

        {step_table_info}

        The UI elements involved in this step are:
        {ui_elements_json}

        Please generate high-quality test data for this step as a JSON object with key-value pairs.
        Each key should be a variable name for the test data, and each value should be the test data value.

        IMPORTANT GUIDELINES:
        1. Generate REALISTIC data that would be used in a real-world scenario, not generic placeholders
        2. If the step involves form submission, include ALL required fields for that form
        3. If the step involves validation testing, include both valid and invalid test data
        4. For text fields, use realistic content (not "test_fieldname")
        5. For emails, use realistic formats like "<EMAIL>"
        6. For names, use realistic full names like "John Smith" or "Maria Rodriguez"
        7. For addresses, use realistic street addresses, cities, states, and zip codes
        8. For credit cards, use valid test card numbers (e.g., "****************" for Visa)
        9. For dates, use proper date formats appropriate to the context
        10. If the step table has test data parameters in {{{{param}}}} format, prioritize generating values for these parameters

        Example of high-quality test data:
        {{
          "email": "<EMAIL>",
          "password": "SecurePass123!",
          "first_name": "John",
          "last_name": "Smith",
          "address": "123 Main Street",
          "city": "Boston",
          "state": "MA",
          "zip_code": "02108",
          "credit_card": "****************",
          "expiry_date": "12/25",
          "cvv": "123"
        }}

        Return ONLY the JSON object with no additional text or explanation.
        """
        logger.info("Successfully built main prompt")
        logger.info(f"Prompt length: {len(prompt)} characters")

        # Log a truncated version of the prompt for debugging
        if len(prompt) > 500:
            logger.info(f"Prompt (first 200 chars): {prompt[:200]}...")
            logger.info(f"Prompt (last 200 chars): ...{prompt[-200:]}")
        else:
            logger.info(f"Full prompt: {prompt}")

        logger.info("=== EXITING generate_test_data_prompt ===")
        return prompt
    except Exception as e:
        logger.error(f"Error generating test data prompt: {e}")
        logger.error(f"Exception details: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return f"Error: {str(e)}"

def generate_test_cases_data_prompt(test_cases):
    """
    Generate a prompt for test data dictionary generation based on multiple test cases.

    Args:
        test_cases (list): List of test cases

    Returns:
        str: The prompt for test data dictionary generation
    """
    try:
        # Initialize test data with common defaults
        test_data = {
            # Login data
            "valid_email": "<EMAIL>",
            "invalid_email": "invalid.email",
            "valid_password": "Password123!",
            "invalid_password": "pass",

            # User data
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "phone_number": "1234567890",
            "mobile_number": "9876543210",

            # Payment data
            "card_number": "****************",
            "card_expiry": "12/25",
            "card_cvv": "123",
            "card_holder": "John Doe"
        }

        # Prepare the prompt for AI
        test_cases_summary = []
        for tc in test_cases[:5]:  # Limit to first 5 test cases to avoid token limits
            if isinstance(tc, dict):
                tc_id = tc.get('Test Case ID', '')
                objective = tc.get('Test Case Objective', '')
                steps = []
                for step in tc.get('Steps', []):
                    if isinstance(step, dict):
                        steps.append({
                            "action": step.get('Test Steps', ''),
                            "expected": step.get('Expected Result', '')
                        })
                test_cases_summary.append({
                    "id": tc_id,
                    "objective": objective,
                    "steps": steps[:3]  # Limit to first 3 steps
                })

        prompt = f"""
        I need to generate test data for the following test cases:
        {json.dumps(test_cases_summary, indent=2)}

        Please enhance this existing test data with more appropriate values based on the test cases:
        {json.dumps(test_data, indent=2)}

        Return only a JSON object with the enhanced test data. Keep the same keys but improve the values.
        """

        return prompt
    except Exception as e:
        logger.error(f"Error generating test cases data prompt: {e}")
        return f"Error: {str(e)}"

# The generate_test_script_prompt function has been moved to core.prompt_builder
# and is imported at the top of this file

def validate_generated_script(
    script_content: str,
    test_case: Dict[str, Any],
    step_table_entry: Optional[Dict[str, Any]] = None,
    test_data: Optional[Dict[str, Any]] = None,
    element_matches: Optional[Dict[str, Any]] = None,
    api_key: Optional[str] = None,
    model_name: str = "gemini-2.0-flash",
    use_transparent_scoring: bool = True
) -> Dict[str, Any]:
    """
    Validate a generated test script using AI analysis.

    This function performs automated code quality validation on generated test scripts,
    analyzing syntax, best practices, WebDriver usage, and integration with test data.
    All AI calls are routed through generate_llm_response for centralized logging.

    Args:
        script_content: The generated test script to validate
        test_case: The test case information for context
        step_table_entry: The step table entry for context
        test_data: Test data used in the script
        element_matches: Element matches used in the script
        api_key: Google AI API key
        model_name: AI model to use for validation
        use_transparent_scoring: Whether to use transparent scoring (default: True)

    Returns:
        Dict containing validation results with keys:
        - quality_score: Integer score 0-100
        - syntax_valid: Boolean indicating syntax validity
        - issues_found: List of issues with category, severity, description
        - recommendations: List of improvement recommendations
        - confidence_rating: String indicating AI confidence (low/medium/high)
        - ready_for_execution: Boolean indicating if script is ready to run
        - validation_error: String error message if validation failed
        - transparent_scoring: Dict with detailed scoring breakdown (if enabled)
    """
    try:
        # Import the validation prompt generator and transparent scoring
        from core.prompt_builder import generate_script_validation_prompt
        from core.transparent_scoring import TransparentScorer, create_transparent_validation_result

        logger.info(f"Starting script validation for test case {test_case.get('Test Case ID', 'Unknown')}")
        logger.info(f"Using transparent scoring: {use_transparent_scoring}")

        # Initialize transparent scoring if enabled
        transparent_result = None
        if use_transparent_scoring:
            logger.info("Performing transparent scoring analysis")
            scorer = TransparentScorer()
            transparent_result = scorer.score_script(
                script_content=script_content,
                test_case=test_case,
                step_table_entry=step_table_entry,
                test_data=test_data,
                element_matches=element_matches
            )
            logger.info(f"Transparent scoring completed: {transparent_result.total_score}/100")

        # Generate the validation prompt for AI analysis (if needed for recommendations)
        validation_prompt = generate_script_validation_prompt(
            script_content=script_content,
            test_case=test_case,
            step_table_entry=step_table_entry,
            test_data=test_data,
            element_matches=element_matches
        )

        # If transparent scoring is enabled and successful, use it as primary result
        if use_transparent_scoring and transparent_result:
            logger.info("Using transparent scoring as primary validation method")

            # Optionally get AI recommendations to supplement transparent scoring
            ai_recommendations = []
            try:
                # Create context for logging
                context = {
                    "test_case_id": test_case.get('Test Case ID', 'Unknown'),
                    "step_no": step_table_entry.get('step_no', '1') if step_table_entry else '1',
                    "script_length": len(script_content),
                    "has_test_data": bool(test_data),
                    "has_element_matches": bool(element_matches),
                    "transparent_score": transparent_result.total_score
                }

                # Call AI for additional recommendations only
                logger.info(f"Getting AI recommendations to supplement transparent scoring")
                response_text = generate_llm_response(
                    prompt=validation_prompt,
                    model_name=model_name,
                    api_key=api_key,
                    context=context,
                    category="script_validation_recommendations",
                    function_name="validate_generated_script"
                )

                # Extract recommendations from AI response
                if response_text and response_text.strip():
                    import json
                    response_clean = response_text.strip()
                    if response_clean.startswith('```json'):
                        response_clean = response_clean.split('```json')[1].split('```')[0].strip()
                    elif response_clean.startswith('```'):
                        response_clean = response_clean.split('```')[1].split('```')[0].strip()

                    ai_response = json.loads(response_clean)
                    ai_recommendations = ai_response.get('recommendations', [])
                    logger.info(f"Extracted {len(ai_recommendations)} AI recommendations")

            except Exception as ai_error:
                logger.warning(f"Failed to get AI recommendations: {ai_error}")
                ai_recommendations = []

            # Create final result using transparent scoring
            final_result = create_transparent_validation_result(
                transparent_result=transparent_result,
                ai_recommendations=ai_recommendations
            )

            logger.info(f"Transparent validation completed - Quality Score: {final_result['quality_score']}, Ready: {final_result['ready_for_execution']}")
            return final_result

        # Fallback to traditional AI-only validation
        logger.info("Using traditional AI-only validation")

        # Create context for logging
        context = {
            "test_case_id": test_case.get('Test Case ID', 'Unknown'),
            "step_no": step_table_entry.get('step_no', '1') if step_table_entry else '1',
            "script_length": len(script_content),
            "has_test_data": bool(test_data),
            "has_element_matches": bool(element_matches)
        }

        # Call the AI through centralized function
        logger.info(f"Sending script validation request to {model_name}")
        response_text = generate_llm_response(
            prompt=validation_prompt,
            model_name=model_name,
            api_key=api_key,
            context=context,
            category="script_validation",
            function_name="validate_generated_script"
        )

        if not response_text or not response_text.strip():
            logger.error("Empty response from AI validation")
            return {
                "quality_score": 0,
                "syntax_valid": False,
                "issues_found": [{"category": "validation", "severity": "high", "description": "AI validation failed - empty response"}],
                "recommendations": ["Retry validation or review script manually"],
                "confidence_rating": "low",
                "ready_for_execution": False,
                "validation_error": "Empty response from AI validation"
            }

        # Parse the JSON response
        try:
            import json
            # Extract JSON from response (handle potential markdown formatting)
            response_clean = response_text.strip()
            if response_clean.startswith('```json'):
                response_clean = response_clean.split('```json')[1].split('```')[0].strip()
            elif response_clean.startswith('```'):
                response_clean = response_clean.split('```')[1].split('```')[0].strip()

            validation_results = json.loads(response_clean)

            # Validate the response structure
            required_keys = ['quality_score', 'syntax_valid', 'issues_found', 'recommendations', 'confidence_rating', 'ready_for_execution']
            for key in required_keys:
                if key not in validation_results:
                    logger.warning(f"Missing key '{key}' in validation response, adding default")
                    if key == 'quality_score':
                        validation_results[key] = 50
                    elif key == 'syntax_valid':
                        validation_results[key] = True
                    elif key in ['issues_found', 'recommendations']:
                        validation_results[key] = []
                    elif key == 'confidence_rating':
                        validation_results[key] = 'medium'
                    elif key == 'ready_for_execution':
                        validation_results[key] = True

            # Ensure quality_score is within valid range
            if not isinstance(validation_results['quality_score'], (int, float)) or validation_results['quality_score'] < 0 or validation_results['quality_score'] > 100:
                logger.warning(f"Invalid quality_score: {validation_results['quality_score']}, setting to 50")
                validation_results['quality_score'] = 50

            logger.info(f"Script validation completed - Quality Score: {validation_results['quality_score']}, Ready: {validation_results['ready_for_execution']}")
            return validation_results

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI validation response as JSON: {e}")
            logger.error(f"Raw response: {response_text[:500]}...")
            return {
                "quality_score": 50,
                "syntax_valid": True,
                "issues_found": [{"category": "validation", "severity": "medium", "description": "AI response parsing failed"}],
                "recommendations": ["Review script manually", "Retry validation"],
                "confidence_rating": "low",
                "ready_for_execution": True,
                "validation_error": f"Failed to parse AI response: {str(e)}"
            }

    except Exception as e:
        logger.error(f"Error during script validation: {e}", exc_info=True)
        return {
            "quality_score": 0,
            "syntax_valid": False,
            "issues_found": [{"category": "validation", "severity": "high", "description": f"Validation error: {str(e)}"}],
            "recommendations": ["Review script manually", "Check validation configuration"],
            "confidence_rating": "low",
            "ready_for_execution": False,
            "validation_error": str(e)
        }

def optimize_script_with_ai(
    combined_script: str,
    *,
    model_name: str = "gemini-2.0-flash",  # Using Gemini 2.0 Flash model
    api_key: Optional[str] = None,
    max_chars: int = 30_000,          # larger budget for optimization
    context: Optional[Dict[str, Any]] = None,
    chunk_mode: bool = False,         # whether to process in chunks
    chunk_info: Optional[Dict[str, Any]] = None  # info about the current chunk
) -> str:
    """
    Use an LLM to optimize a combined PyTest script with comprehensive logging.

    This function takes a combined script that was created by merging multiple step scripts
    and optimizes it into a well-structured, cohesive PyTest module following best practices.

    Args:
        combined_script (str): The combined script to optimize
        model_name (str): The AI model to use
        api_key (Optional[str]): API key for Google AI
        max_chars (int): Maximum characters allowed for the script
        context (Optional[Dict[str, Any]]): Additional context information
        chunk_mode (bool): Whether to process in chunks for large scripts
        chunk_info (Optional[Dict[str, Any]]): Information about the current chunk

    Returns:
        str: The optimized script
    """
    import logging
    import uuid
    import time

    logger = logging.getLogger("ScriptWeaver.ai")

    # Generate a unique request ID for this optimization operation
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Create context information if not provided
    optimization_context = context or {}
    optimization_context.update({
        'script_length': len(combined_script),
        'operation': 'script_optimization',
        'chunk_mode': chunk_mode
    })

    if chunk_info:
        optimization_context.update({
            'chunk_number': chunk_info.get('chunk_number', 0),
            'total_chunks': chunk_info.get('total_chunks', 1),
            'chunk_type': chunk_info.get('chunk_type', 'full')
        })

    logger.info(f"Starting script optimization operation [Request ID: {request_id}]")

    # ── trivial cases ────────────────────────────────────────────────────
    if not combined_script:
        logger.warning(f"Empty script provided for optimization [Request ID: {request_id}]")

        # Log the trivial case
        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt="Empty script provided",
            response="Returning empty script",
            model_name="N/A",
            request_id=request_id,
            context=optimization_context,
            category="script_optimization_trivial"
        )

        return ""

    # ── token-budget guard: trim huge script if needed ───────────────
    original_script_length = len(combined_script)
    if original_script_length > max_chars and not chunk_mode:
        logger.warning(f"Script too large ({original_script_length} chars) - recommend using chunk mode [Request ID: {request_id}]")

        # Log the warning
        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt=f"Script too large ({original_script_length} chars)",
            response="Script exceeds maximum size, recommend using chunk mode",
            model_name="N/A",
            request_id=request_id,
            context=optimization_context,
            category="script_optimization_warning"
        )

    try:
        # Create the system prompt for script optimization
        system_prompt = """
You are a meticulous senior QA-automation engineer. Your task is to merge multiple step-level PyTest scripts into one single, cohesive, and runnable test module while maintaining all functionality.
"""

        # Create the user prompt based on whether we're in chunk mode or not
        if chunk_mode and chunk_info:
            chunk_type = chunk_info.get('chunk_type', 'full')
            chunk_number = chunk_info.get('chunk_number', 0)
            total_chunks = chunk_info.get('total_chunks', 1)

            if chunk_type == 'imports':
                user_prompt = f"""
I'm providing you with the imports section of a PyTest script (chunk {chunk_number} of {total_chunks}).
Please optimize these imports by:
1. Removing duplicate imports
2. Organizing imports in a logical order (standard library, third-party, local)
3. Combining related imports
4. Removing any unused imports if obvious

Here's the imports section:
```python
{combined_script}
```

Return ONLY the optimized imports section without any explanations or markdown formatting.
"""
            elif chunk_type == 'fixtures':
                user_prompt = f"""
I'm providing you with the fixtures section of a PyTest script (chunk {chunk_number} of {total_chunks}).
Please optimize these fixtures by:
1. Removing duplicate fixtures
2. Improving fixture documentation
3. Enhancing fixture reusability
4. Ensuring proper teardown/cleanup

Here's the fixtures section:
```python
{combined_script}
```

Return ONLY the optimized fixtures section without any explanations or markdown formatting.
"""
            elif chunk_type == 'test_functions':
                user_prompt = f"""
I'm providing you with the test functions section of a PyTest script (chunk {chunk_number} of {total_chunks}).
Please optimize these test functions by:
1. Ensuring proper test function naming
2. Adding clear docstrings
3. Improving assertions and error messages
4. Enhancing test readability
5. Maintaining all test functionality

Here's the test functions section:
```python
{combined_script}
```

Return ONLY the optimized test functions section without any explanations or markdown formatting.
"""
            else:  # 'full' or any other type
                user_prompt = f"""
I'm providing you with a portion of a PyTest script (chunk {chunk_number} of {total_chunks}) that needs optimization.
Please optimize this script chunk by:
1. Improving code structure and organization
2. Enhancing readability and maintainability
3. Following PyTest best practices
4. Maintaining all functionality

Here's the script chunk:
```python
{combined_script}
```

Return ONLY the optimized script chunk without any explanations or markdown formatting.
"""
        else:
            # Full script optimization (not in chunk mode)
            user_prompt = f"""
I'm providing you with a PyTest script that was created by merging multiple step-level scripts.
This script needs to be optimized into a well-structured, cohesive PyTest module.

Please optimize this script by:
1. Refactoring it into a well-structured PyTest module
2. Ensuring proper test setup and teardown with appropriate fixtures
3. Removing redundancies and optimizing the code
4. Maintaining all functionality from the original steps
5. Following best practices for PyTest automation
6. Adding clear comments explaining the optimization process
7. Preserving all imports, assertions, and test logic
8. **CRITICAL**: Ensure all test functions use 'browser' as the WebDriver fixture parameter, never 'driver'
9. **CRITICAL**: Ensure all WebDriver operations reference the 'browser' parameter consistently
10. **CRITICAL**: Test functions must reference the module-level `test_data`
    dictionary (or `load_test_data()`), never a fixture parameter.
9. Keep @pytest.mark.order(N) decorators and a single `import pytest`
10. Test functions must use the module-level `test_data` dict, not a fixture
11. Do not add screenshot/logging fixtures; conftest.py handles that

Here's the combined script:
```python
{combined_script}
```

Return ONLY the optimized script without any explanations or markdown formatting.
"""

        # Log the prompt generation
        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt=user_prompt,
            response="<prompt generation>",
            model_name=model_name,
            request_id=request_id,
            context=optimization_context,
            category="script_optimization_prompt",
            is_prompt_generation=True
        )

        # Call the LLM to optimize the script
        response = generate_llm_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_name,
            api_key=api_key,
            function_name="optimize_script_with_ai",
            parent_request_id=request_id,
            context=optimization_context,
            category="script_optimization"
        )

        # Clean the response to remove markdown code blocks
        from .ai_helpers import clean_llm_response
        optimized_script = clean_llm_response(response, "python")

        # Calculate total time
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # Log the successful optimization
        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt=f"Script length: {len(combined_script)} chars",
            response=f"Optimized script length: {len(optimized_script)} chars",
            model_name=model_name,
            request_id=request_id,
            context={
                **optimization_context,
                'original_script_length': len(combined_script),
                'optimized_script_length': len(optimized_script),
                'total_processing_time_ms': total_time_ms
            },
            latency_ms=total_time_ms,
            category="script_optimization_success"
        )

        return optimized_script

    except Exception as exc:
        # Calculate total time even for errors
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        logger.error(f"Script optimization failed: {exc} [Request ID: {request_id}]")

        # Log the error with detailed information
        error_context = optimization_context.copy()
        error_context.update({
            'error_type': type(exc).__name__,
            'total_processing_time_ms': total_time_ms
        })

        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt=f"Script length: {len(combined_script)} chars",
            response=f"ERROR: {str(exc)}",
            model_name="N/A",
            request_id=request_id,
            context=error_context,
            error=exc,
            latency_ms=total_time_ms,
            category="script_optimization_error"
        )

        # Return the original script if optimization fails
        return combined_script

def merge_scripts_with_ai(
    previous_script: str,
    current_script: str,
    *,
    model_name: str = "gemini-2.0-flash",
    api_key: Optional[str] = None,
    max_chars: int = 10_000,          # budget safeguard
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Use an LLM to merge two partial PyTest scripts with comprehensive logging.
    Falls back to simple concatenation if the AI result is unusable.

    Args:
        previous_script (str): The script from previous steps
        current_script (str): The script for the current step
        model_name (str): The AI model to use
        api_key (Optional[str]): API key for Google AI
        max_chars (int): Maximum characters allowed for each script
        context (Optional[Dict[str, Any]]): Additional context information

    Returns:
        str: The merged script or concatenated scripts if merge fails
    """
    import logging
    logger = logging.getLogger("ScriptWeaver.ai")

    # Generate a unique request ID for this merge operation
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Create context information if not provided
    merge_context = context or {}
    merge_context.update({
        'previous_script_length': len(previous_script),
        'current_script_length': len(current_script),
        'operation': 'script_merge'
    })

    logger.info(f"Starting script merge operation [Request ID: {request_id}]")

    # ── trivial cases ────────────────────────────────────────────────────
    if not previous_script:
        logger.info(f"No previous script – returning current script unchanged [Request ID: {request_id}]")

        # Log the trivial case
        log_ai_interaction(
            function_name="merge_scripts_with_ai",
            prompt="No previous script provided",
            response="Returning current script unchanged",
            model_name="N/A",
            request_id=request_id,
            context=merge_context,
            category="script_merge_trivial"
        )

        return current_script or ""

    if not current_script:
        logger.warning(f"Current script empty – returning previous script unchanged [Request ID: {request_id}]")

        # Log the trivial case
        log_ai_interaction(
            function_name="merge_scripts_with_ai",
            prompt="Current script empty",
            response="Returning previous script unchanged",
            model_name="N/A",
            request_id=request_id,
            context=merge_context,
            category="script_merge_trivial"
        )

        return previous_script

    # ── token-budget guard: trim huge PART-1 to essentials ───────────────
    original_previous_length = len(previous_script)

    if len(previous_script) + len(current_script) > max_chars:
        keep = []
        for line in previous_script.splitlines():
            if (
                line.startswith(("import ", "from "))
                or line.strip().startswith("@pytest.fixture")
                or (
                    re.match(r"def\s+\w+\(", line)      # any def
                    and not line.strip().startswith("def test_")
                )
            ):
                keep.append(line)
        previous_script_trim = "\n".join(keep)
        logger.info(
            f"Previous script truncated from {len(previous_script)} ➔ {len(previous_script_trim)} chars for token budget [Request ID: {request_id}]"
        )
        previous_script = previous_script_trim
        merge_context['previous_script_truncated'] = True
        merge_context['previous_script_original_length'] = original_previous_length
        merge_context['previous_script_truncated_length'] = len(previous_script_trim)

    # ── build compact guard-railed prompt ────────────────────────────────
    prompt = f"""
You are an expert Python refactorer.

TASK ▸ Merge PART-1 and PART-2 into ONE cohesive PyTest file.

MUST
1. Keep exactly one copy of each import / fixture / helper / variable.
2. Preserve execution order: PART-1 code appears before PART-2 code.
3. Return ONLY valid Python inside **one** ```python fenced block – no commentary.
4. The first non-whitespace character of your answer MUST be a backtick.
5. IMPORTANT: Keep ALL test functions from BOTH scripts, do not drop any test_* functions.
6. **CRITICAL**: Ensure all test functions use 'browser' as the WebDriver fixture parameter, never 'driver'.
7. **CRITICAL**: Ensure all WebDriver operations reference the 'browser' parameter consistently.
8. Every test function must take **exactly one parameter** (`browser`).
   They may reference a global `test_data` dict; do NOT add a fixture param.
9. If BOTH parts define `test_data = {...}`: merge them into one dict

PART-1
```python
{previous_script}
```

--- PART-2 (current step) ---
```python
{current_script}
```
OUTPUT
Return ONLY the merged Python code inside a ```python block.
# merged script starts here
"""

    try:
        # Extract test functions for validation later
        current_test_functions = re.findall(r"def\s+(test_\w+)\s*\(", current_script)
        merge_context['current_test_functions'] = current_test_functions

        # Extract imports for validation later
        imp_regex = re.compile(r"^(?:from\s+([\w\.]+)|import\s+([\w\.]+))", re.MULTILINE)
        current_imports = {m.group(1) or m.group(2) for m in imp_regex.finditer(current_script)}
        merge_context['current_imports'] = list(current_imports)

        # Local import to avoid cycle
        from .ai import generate_llm_response

        # Generate the merged script with enhanced logging
        logger.info(f"Sending merge request to {model_name} [Request ID: {request_id}]")
        merged = generate_llm_response(
            prompt=prompt,
            model_name=model_name,
            api_key=api_key,
            context=merge_context,
            parent_request_id=request_id,
            category="script_merge",
            related_request_ids=None  # Will be filled by generate_llm_response
        )

        # Calculate processing time
        processing_time = time.time() - start_time
        processing_time_ms = processing_time * 1000

        # Strip fence(s)
        if "```" in merged:
            merged = merged.split("```python")[-1].split("```")[0].strip()

        # Update context with results
        merge_context['merged_script_length'] = len(merged)
        merge_context['processing_time_ms'] = processing_time_ms

        # Quick integrity checks
        validation_start_time = time.time()
        validation_results = {}

        # 1) Every test_ func from current must survive
        missing_tests = [
            fn
            for fn in current_test_functions
            if fn not in merged
        ]
        validation_results['missing_test_functions'] = missing_tests

        if missing_tests:
            logger.warning(f"AI merge dropped test functions {missing_tests} – using concatenation approach [Request ID: {request_id}]")

            # Log the validation failure
            log_ai_interaction(
                function_name="merge_scripts_validation_failed",
                prompt="",
                response=f"Validation failed: Missing test functions: {missing_tests}",
                model_name="N/A",
                request_id=str(uuid.uuid4()),
                parent_request_id=request_id,
                context=merge_context,
                category="script_merge_validation"
            )

            # Simple concatenation with a separator
            return f"# Combined script (concatenated due to AI merge failure - missing test functions)\n\n# --- PREVIOUS STEP SCRIPT ---\n{previous_script}\n\n# --- CURRENT STEP SCRIPT ---\n{current_script}"

        # 2) Critical imports present (only if they existed in current)
        critical = {"pytest", "selenium", "os", "time", "selenium.webdriver"}
        missing_imports = []

        for imp in critical:
            if any(i.startswith(imp) for i in current_imports) and imp not in merged:
                missing_imports.append(imp)

        validation_results['missing_critical_imports'] = missing_imports

        if missing_imports:
            logger.warning(f"AI merge dropped critical imports {missing_imports} – using concatenation approach [Request ID: {request_id}]")

            # Log the validation failure
            log_ai_interaction(
                function_name="merge_scripts_validation_failed",
                prompt="",
                response=f"Validation failed: Missing critical imports: {missing_imports}",
                model_name="N/A",
                request_id=str(uuid.uuid4()),
                parent_request_id=request_id,
                context=merge_context,
                category="script_merge_validation"
            )

            # Simple concatenation with a separator
            return f"# Combined script (concatenated due to AI merge failure - missing imports)\n\n# --- PREVIOUS STEP SCRIPT ---\n{previous_script}\n\n# --- CURRENT STEP SCRIPT ---\n{current_script}"

        # 3) Syntax–check merged code
        syntax_error = None
        try:
            compile(
                re.sub(r"^#.*coding[:=].*\n", "", merged),  # strip encoding cookies
                "<merged>",
                "exec",
            )
        except SyntaxError as e:
            syntax_error = str(e)
            validation_results['syntax_error'] = syntax_error

            logger.warning(f"AI merge produced invalid Python code: {e} – using concatenation approach [Request ID: {request_id}]")

            # Log the validation failure
            log_ai_interaction(
                function_name="merge_scripts_validation_failed",
                prompt="",
                response=f"Validation failed: Syntax error: {e}",
                model_name="N/A",
                request_id=str(uuid.uuid4()),
                parent_request_id=request_id,
                context=merge_context,
                category="script_merge_validation"
            )

            # Simple concatenation with a separator
            return f"# Combined script (concatenated due to AI merge failure - syntax error)\n\n# --- PREVIOUS STEP SCRIPT ---\n{previous_script}\n\n# --- CURRENT STEP SCRIPT ---\n{current_script}"

        # Calculate validation time
        validation_time = time.time() - validation_start_time
        validation_time_ms = validation_time * 1000

        # Update context with validation results
        merge_context['validation_time_ms'] = validation_time_ms
        merge_context['validation_results'] = validation_results

        # Log successful validation
        log_ai_interaction(
            function_name="merge_scripts_validation_success",
            prompt="",
            response=f"Validation successful: All tests passed",
            model_name="N/A",
            request_id=str(uuid.uuid4()),
            parent_request_id=request_id,
            context=merge_context,
            category="script_merge_validation"
        )

        logger.info(f"AI merge succeeded – {len(merged)} chars in {processing_time_ms:.2f}ms [Request ID: {request_id}]")
        return merged

    except Exception as exc:
        # Calculate total time even for errors
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        logger.error(f"AI merge failed: {exc} → using concatenation approach [Request ID: {request_id}]")

        # Log the error with detailed information
        error_context = merge_context.copy()
        error_context.update({
            'error_type': type(exc).__name__,
            'total_processing_time_ms': total_time_ms
        })

        log_ai_interaction(
            function_name="merge_scripts_with_ai",
            prompt=f"Previous script length: {len(previous_script)}, Current script length: {len(current_script)}",
            response=f"ERROR: {str(exc)}",
            model_name="N/A",
            request_id=request_id,
            context=error_context,
            error=exc,
            latency_ms=total_time_ms,
            category="script_merge_error"
        )

        # Simple concatenation with a separator
        return f"# Combined script (concatenated due to AI merge failure - exception)\n\n# --- PREVIOUS STEP SCRIPT ---\n{previous_script}\n\n# --- CURRENT STEP SCRIPT ---\n{current_script}"

def generate_test_script(test_case, step_matches, test_data, website_url, step_table_entry=None, api_key=None, state=None):
    """
    Generate a test script for a test case using AI.

    Implements a two-phase script generation process:

    Phase 1 - Step-Specific Script Generation:
    - Generates a script that focuses exclusively on the current test step
    - Does not attempt to merge with previous steps or maintain continuity
    - Produces a clean, isolated script that implements only the current step's functionality
    - Useful for debugging and understanding the specific implementation of the current step

    Phase 2 - Script Merging:
    - Takes the step-specific script from Phase 1
    - Intelligently combines it with previously generated scripts from earlier steps
    - Uses the merge_scripts_with_ai function to maintain proper continuity
    - Handles import deduplication, variable reuse, and proper sequencing
    - Creates a cohesive script that implements all steps up to and including the current one

    This modular approach:
    - Makes debugging easier by allowing inspection of both isolated and merged scripts
    - Creates a clearer separation of concerns in the code
    - Allows for potential future enhancements to either generation or merging processes

    Args:
        test_case (dict or list): The test case(s) to generate a script for
        step_matches (dict): Dictionary of element matches for test steps
        test_data (dict): Dictionary of test data values
        website_url (str): The URL of the website to test
        step_table_entry (dict, optional): The step table entry for the step
        api_key (str, optional): API key for Google AI. If None, use initialized client
        state (StateManager, optional): The application state manager instance for script continuity

    Returns:
        tuple: (merged_script, step_specific_script) where:
            - merged_script (str): The final merged script combining all steps
            - step_specific_script (str): The script for just the current step
    """
    try:
        # DEBUG: Log function entry with key parameters for Step 2 debugging
        logger.info("=== DEBUG: generate_test_script ENTRY ===")
        if step_table_entry:
            step_no = step_table_entry.get('step_no')
            logger.info(f"Processing Step {step_no}")
            logger.info(f"Step Table Entry: {step_table_entry}")

        # DEBUG: Log state information for debugging state transitions
        if state:
            logger.info("=== DEBUG: State Information ===")
            # Log key state attributes that might affect script generation
            if hasattr(state, 'current_step_index'):
                logger.info(f"State current_step_index: {state.current_step_index}")
            if hasattr(state, 'browser_initialized'):
                logger.info(f"State browser_initialized: {state.browser_initialized}")
            if hasattr(state, 'previous_scripts'):
                logger.info(f"State has previous_scripts: {list(state.previous_scripts.keys()) if state.previous_scripts else 'None'}")
            if hasattr(state, 'script_imports'):
                logger.info(f"State has script_imports: {len(state.script_imports) if state.script_imports else 0} imports")
            if hasattr(state, 'script_variables'):
                logger.info(f"State has script_variables: {list(state.script_variables.keys()) if state.script_variables else 'None'}")

        # Create a set to track seen imports, fixtures, and helper functions
        seen_imports = set()

        # Handle case where test_case is a list (take the first item)
        if isinstance(test_case, list):
            if not test_case:
                return "Error: Empty test case list provided.", ""
            test_case = test_case[0]

        # Handle case where step_matches is a list (convert to expected dictionary format)
        if isinstance(step_matches, list):
            logger.info(f"In generate_test_script: step_matches is a list with {len(step_matches)} elements")
            # Convert list to a dictionary format expected by the rest of the function
            if test_case and step_table_entry:
                tc_id = test_case.get('Test Case ID', 'unknown')
                step_no = step_table_entry.get('step_no', '1')

                # Create a properly structured dictionary
                step_matches = {
                    tc_id: {
                        step_no: step_matches
                    }
                }
                logger.info(f"Converted step_matches list to dictionary with TC ID: {tc_id}, Step: {step_no}")
            else:
                logger.warning("Cannot convert step_matches list to dictionary - missing test_case or step_table_entry")
                # Create an empty dictionary as fallback
                step_matches = {}

        # Generate the enhanced prompt with validation feedback
        prompt = generate_enhanced_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            step_table_entry=step_table_entry,
            state=state,
            include_validation_feedback=True
        )

        # DEBUG: Log the prompt structure (truncated for readability)
        if step_table_entry and step_table_entry.get('step_no') == '2':
            logger.info("=== DEBUG: Prompt for Step 2 ===")
            # Log key sections of the prompt
            prompt_lines = prompt.split('\n')
            context_section = []
            element_matches_section = []
            test_data_section = []

            in_element_matches = False
            in_test_data = False

            for line in prompt_lines:
                if "### ELEMENT_MATCHES (JSON)" in line:
                    in_element_matches = True
                    in_test_data = False
                    continue
                elif "### TEST_DATA (JSON)" in line:
                    in_element_matches = False
                    in_test_data = True
                    continue
                elif "### PREVIOUS-STEP SUMMARY" in line:
                    in_element_matches = False
                    in_test_data = False
                    continue

                if in_element_matches:
                    element_matches_section.append(line)
                elif in_test_data:
                    test_data_section.append(line)
                elif "CONTEXT" in line or "Test-Case ID" in line or "Objective" in line or "Current Step No." in line or "Action" in line or "Expected" in line:
                    context_section.append(line)

            logger.info(f"Context Section: {context_section}")
            logger.info(f"Element Matches Section (first 5 lines): {element_matches_section[:5]}")
            logger.info(f"Test Data Section (first 5 lines): {test_data_section[:5]}")

        # =====================================================================
        # PHASE 1: Generate a step-specific script that focuses only on the current test step
        # =====================================================================
        # This phase generates a script that implements only the current step's functionality,
        # without any attempt to merge with previous steps or maintain continuity.
        # The resulting script is clean, isolated, and focused on a single task.
        logger.info("PHASE 1: Generating step-specific script")

        # Log the prompt for script generation separately with enhanced prompt generation tracing
        prompt_log_filepath, prompt_request_id = log_ai_interaction(
            function_name="generate_test_script_prompt",
            prompt=prompt,
            response="See generate_llm_response log for the response",
            model_name="gemini-1.5-flash",
            capture_stack=True,
            is_prompt_generation=True,  # Enable prompt generation tracing
            context={
                'test_case_id': test_case.get('Test Case ID', 'unknown'),
                'step_no': step_table_entry.get('step_no') if step_table_entry else 'unknown',
                'action': step_table_entry.get('action') if step_table_entry else 'unknown',
                'website_url': website_url,
                'has_step_matches': bool(step_matches),
                'has_test_data': bool(test_data),
                'has_state': bool(state)
            }
        )

        # Generate the script with enhanced logging, linking to the prompt log
        step_specific_script = generate_llm_response(
            prompt=prompt,
            api_key=api_key,
            category="script_generation",
            related_request_ids=[prompt_request_id],  # Link to the prompt log
            context={
                'test_case_id': test_case.get('Test Case ID', 'unknown'),
                'step_no': step_table_entry.get('step_no') if step_table_entry else 'unknown',
                'action': step_table_entry.get('action') if step_table_entry else 'unknown',
                'prompt_log_filepath': prompt_log_filepath,
                'prompt_request_id': prompt_request_id
            }
        )

        # Clean up the response to extract code
        if "```python" in step_specific_script:
            code_start = step_specific_script.find("```python") + 10
            code_end = step_specific_script.rfind("```")
            step_specific_script = step_specific_script[code_start:code_end].strip()
        elif "```" in step_specific_script:
            code_start = step_specific_script.find("```") + 3
            code_end = step_specific_script.rfind("```")
            step_specific_script = step_specific_script[code_start:code_end].strip()

        # Process the script to remove duplicate imports
        if step_specific_script:
            # Split the script into lines
            script_lines = step_specific_script.split('\n')
            processed_lines = []

            # Process each line
            for line in script_lines:
                # Check if the line is an import statement
                if line.strip().startswith(('import ', 'from ')):
                    if line.strip() not in seen_imports:
                        seen_imports.add(line.strip())
                        processed_lines.append(line)
                # Check if the line is a fixture or helper function definition
                elif line.strip().startswith(('@pytest.fixture', 'def ')):
                    # For function definitions, we need to check the entire function
                    # This is a simplification - in a real implementation, you'd need to
                    # track indentation to identify the full function
                    if line.strip() not in seen_imports:
                        seen_imports.add(line.strip())
                        processed_lines.append(line)
                else:
                    processed_lines.append(line)

            # Reassemble the script
            step_specific_script = '\n'.join(processed_lines)

            # Add WebDriverManager log level setting after the first import os statement
            if 'import os' in step_specific_script and 'os.environ["WDM_LOG_LEVEL"] = "0"' not in step_specific_script:
                step_specific_script = step_specific_script.replace(
                    'import os',
                    'import os\nos.environ["WDM_LOG_LEVEL"] = "0"'
                )

        # Post-process the code to ensure it only implements the selected step
        # Find the current step based on step_table_entry
        current_step = None
        if step_table_entry:
            step_no = step_table_entry.get('step_no')
            if step_no:
                for step in test_case.get('Steps', []):
                    if str(step.get('Step No')) == str(step_no):
                        current_step = step
                        break

                # DEBUG: Log if we found the step or not for post-processing
                if current_step:
                    logger.info(f"Found matching step for post-processing: Step {step_no} - {current_step.get('Test Steps')}")
                else:
                    logger.warning(f"Could not find matching step {step_no} for post-processing")

        # If we couldn't find the step, use the first step as fallback
        if not current_step and test_case.get('Steps'):
            steps = test_case.get('Steps')
            if steps and len(steps) > 0:
                logger.warning("Could not find matching step for post-processing, using first step as fallback")
                current_step = steps[0]
                logger.info(f"Using fallback step for post-processing: {current_step.get('Test Steps')}")
            else:
                logger.warning("Test case has empty Steps list, cannot use fallback step for post-processing")

        # Check if this is a navigation step
        step_action = current_step.get('Test Steps', '').lower() if current_step else ''
        is_navigation_step = any(nav in step_action for nav in ["navigate", "go to", "open", "visit", "browse to"])

        # DEBUG: Log step type identification
        logger.info(f"=== DEBUG: Step Type Identification ===")
        logger.info(f"Step Action: '{step_action}'")
        logger.info(f"Is Navigation Step: {is_navigation_step}")

        # DEBUG: For Step 2 specifically, log more details about the step
        if step_table_entry and step_table_entry.get('step_no') == '2':
            logger.info(f"=== DEBUG: Step 2 Details ===")
            logger.info(f"Full Step Info: {current_step}")
            if step_table_entry:
                logger.info(f"Step Table Entry Action: {step_table_entry.get('action')}")
                logger.info(f"Step Table Entry Locator Strategy: {step_table_entry.get('locator_strategy')}")
                logger.info(f"Step Table Entry Element: {step_table_entry.get('element')}")

            # Log the first 10 lines of the generated script for Step 2
            if step_specific_script:
                script_lines = step_specific_script.split('\n')
                logger.info(f"First 10 lines of Step 2 script before post-processing:")
                for i, line in enumerate(script_lines[:10]):
                    logger.info(f"  Line {i+1}: {line}")

        # For navigation steps, filter out any code that checks for UI elements
        if is_navigation_step:
            # List of patterns that should not be in navigation step code
            forbidden_patterns = [
                "find_element",
                "visibility_of_element_located",
                "presence_of_element_located",
                "element_to_be_clickable",
                "By.ID",
                "By.CSS_SELECTOR",
                "By.XPATH",
                "By.CLASS_NAME",
                "By.NAME",
                "By.TAG_NAME",
                "By.LINK_TEXT",
                "By.PARTIAL_LINK_TEXT"
            ]

            # Check if the expected result explicitly mentions title verification
            expected_result = current_step.get('Expected Result', '').lower() if current_step else ''
            title_verification_required = "title" in expected_result

            # DEBUG: Log expected result and title verification requirement
            logger.info(f"Expected Result: '{expected_result}'")
            logger.info(f"Title Verification Required: {title_verification_required}")

            # If title verification is not required, add title-related patterns to forbidden list
            if not title_verification_required:
                # Use simple string patterns without regex special characters
                forbidden_patterns.extend(["driver.title", "assert", "in driver.title"])
                logger.info("Added title-related patterns to forbidden list")

            # Split the code into lines for processing
            code_lines = step_specific_script.split('\n')
            filtered_lines = []
            removed_lines = []

            # Process each line
            for line in code_lines:
                # Skip lines with forbidden patterns unless they're in comments
                if any(pattern in line for pattern in forbidden_patterns) and not line.strip().startswith('#'):
                    # Replace with a comment explaining why it was removed
                    filtered_lines.append(f"# Removed: {line.strip()} - Not needed for navigation step")
                    removed_lines.append(line.strip())
                else:
                    filtered_lines.append(line)

            # DEBUG: Log removed lines
            if removed_lines:
                logger.info(f"Removed {len(removed_lines)} lines from navigation step script:")
                for line in removed_lines[:5]:  # Log first 5 removed lines
                    logger.info(f"  Removed: {line}")
                if len(removed_lines) > 5:
                    logger.info(f"  ... and {len(removed_lines) - 5} more lines")

            # Reassemble the code
            step_specific_script = '\n'.join(filtered_lines)

        # --- Replace placeholder URLs with actual website_url ---
        if step_specific_script:
            # Fix for URL placeholders with curly braces
            step_specific_script = step_specific_script.replace("{website_url}", website_url)
            step_specific_script = step_specific_script.replace("{{website_url}}", website_url)
            step_specific_script = step_specific_script.replace("{{{website_url}}}", website_url)

            # Fix for URLs that already have curly braces around them
            # Pattern for url = "{url}" - using string concatenation to avoid escape issues
            url_pattern = r'url\s*=\s*[\'"]\{' + '([^}]+)' + r'\}[\'"]\s*'
            step_specific_script = re.sub(url_pattern, r'url = "\1"', step_specific_script)

            # Pattern for EC.url_to_be("{url}") - using string concatenation to avoid escape issues
            url_to_be_pattern = r'EC\.url_to_be\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(url_to_be_pattern, r'EC.url_to_be("\1")', step_specific_script)

            # Pattern for EC.url_contains("{url}") - using string concatenation to avoid escape issues
            url_contains_pattern = r'EC\.url_contains\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(url_contains_pattern, r'EC.url_contains("\1")', step_specific_script)

            # Pattern for EC.url_matches("{url}") - using string concatenation to avoid escape issues
            url_matches_pattern = r'EC\.url_matches\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(url_matches_pattern, r'EC.url_matches("\1")', step_specific_script)

            # Pattern for driver.get("{url}") - using string concatenation to avoid escape issues
            driver_get_pattern = r'driver\.get\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(driver_get_pattern, r'driver.get("\1")', step_specific_script)

            # General pattern to catch any remaining URL-related methods with curly braces
            # Using string concatenation to avoid escape issues
            general_url_pattern = r'(\w+\.\w+)\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'

            # Function to process matches for the general pattern
            def replace_url_in_method(match):
                method = match.group(1)
                url = match.group(2)
                return f'{method}("{url}")'

            # Apply the general pattern replacement
            step_specific_script = re.sub(general_url_pattern, replace_url_in_method, step_specific_script)

            # Special handling for navigation steps with test data parameters
            # Check if we have a step table entry with a test_data_param for a navigation step
            if step_table_entry:
                # Handle navigation steps
                if step_table_entry.get('action', '').lower() in ['navigate', 'go to', 'open', 'visit']:
                    # Get expected result
                    expected_result = step_table_entry.get('expected_result', '')

                    # Fix incorrect title assertions for navigation steps
                    if 'login' in expected_result.lower() and 'title' not in expected_result.lower():
                        # Remove any assertions about page title containing "Login"
                        # Using a safer pattern construction to avoid escape issues
                        # Break the pattern into parts to avoid escape sequence issues
                        title_pattern_parts = [
                            r'assert\s+[\'"]Login[\'"]',
                            r'assert\s+[\'"]login[\'"]'
                        ]
                        for pattern in title_pattern_parts:
                            pattern_with_title = pattern + r'\s+in\s+driver\.title'
                            step_specific_script = re.sub(pattern_with_title,
                                                        '# Navigation step - verifying URL is sufficient',
                                                        step_specific_script)

                    # If there's a test_data_param, handle URL parameters
                    if step_table_entry.get('test_data_param'):
                        # Extract the parameter name without curly braces
                        param_name = step_table_entry.get('test_data_param', '')
                        param_name = param_name.strip('{}')

                        # Look for patterns like url = "{param_name}" or url = "{{param_name}}"
                        # Use raw strings with properly escaped curly braces
                        param_patterns = [
                            r'url\s*=\s*[\'"]\{' + param_name + r'\}[\'"]',
                            r'url\s*=\s*[\'"]{{' + param_name + r'}}[\'"]'
                        ]

                        # If the website URL is a placeholder, replace it with the actual URL
                        if param_name.lower() in ['login_page_url', 'website_url', 'application_url', 'base_url']:
                            for pattern in param_patterns:
                                step_specific_script = re.sub(pattern, f'url = "{website_url}"', step_specific_script)

            # Also replace any legacy placeholders for robustness
            for placeholder in ["YOUR_LOGIN_URL_HERE", "YOUR_APPLICATION_URL", "URL_HERE"]:
                if placeholder in step_specific_script:
                    step_specific_script = step_specific_script.replace(placeholder, website_url)

        # =====================================================================
        # PHASE 2: Merge with previous scripts to maintain continuity
        # =====================================================================
        # This phase takes the step-specific script from Phase 1 and intelligently
        # combines it with previously generated scripts from earlier steps.
        # The merge process handles:
        # - Import deduplication
        # - Variable reuse
        # - Proper sequencing of actions
        # - Maintaining browser state between steps
        # - Ensuring test flow continuity
        logger.info("PHASE 2: Merging with previous scripts to maintain continuity")

        # Start with the step-specific script as our merged script
        # (If no previous scripts exist, the step-specific script becomes the merged script)
        merged_script = step_specific_script

        # DEBUG: For Step 2, log the step-specific script before merging
        if step_table_entry and step_table_entry.get('step_no') == '2':
            logger.info("=== DEBUG: Step 2 Script Before Merging ===")
            if step_specific_script:
                # Log a summary of the script (first 10 lines and last 5 lines)
                script_lines = step_specific_script.split('\n')
                logger.info(f"Step 2 script length: {len(script_lines)} lines")
                logger.info("First 10 lines:")
                for i, line in enumerate(script_lines[:10]):
                    logger.info(f"  {i+1}: {line}")
                if len(script_lines) > 15:
                    logger.info("Last 5 lines:")
                    for i, line in enumerate(script_lines[-5:]):
                        logger.info(f"  {len(script_lines)-4+i}: {line}")
            else:
                logger.warning("Step 2 script is empty or None before merging")

        # Get the previous step's script if available
        previous_script = None
        if state and step_table_entry:
            step_no = step_table_entry.get('step_no')
            if step_no and hasattr(state, 'previous_scripts'):
                # Find the previous step number
                try:
                    current_step_no = int(step_no)
                    if current_step_no > 1:
                        previous_step_no = str(current_step_no - 1)
                        previous_script = state.previous_scripts.get(previous_step_no)
                        if previous_script:
                            logger.info(f"Found previous script for step {previous_step_no} ({len(previous_script)} chars)")

                            # DEBUG: For Step 2, log details about the previous script
                            if step_no == '2':
                                logger.info("=== DEBUG: Previous Script (Step 1) Details ===")
                                prev_script_lines = previous_script.split('\n')
                                logger.info(f"Previous script length: {len(prev_script_lines)} lines")
                                logger.info("First 10 lines of previous script:")
                                for i, line in enumerate(prev_script_lines[:10]):
                                    logger.info(f"  {i+1}: {line}")
                        else:
                            logger.info(f"No previous script found for step {previous_step_no}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid step number: {step_no}")

        # Merge the current script with the previous script if available
        if previous_script:
            logger.info("Merging current script with previous script via AI with enhanced prompt generation tracing")
            merged_script = merge_scripts_with_ai(
                previous_script=previous_script,
                current_script=step_specific_script,
                api_key=api_key,
                context={
                    'test_case_id': test_case.get('Test Case ID', 'unknown'),
                    'step_no': step_table_entry.get('step_no') if step_table_entry else 'unknown',
                    'action': step_table_entry.get('action') if step_table_entry else 'unknown',
                    'previous_script_length': len(previous_script),
                    'current_script_length': len(step_specific_script),
                    'is_prompt_generation': True  # Enable prompt generation tracing
                }
            )
            logger.info(f"Merged script generated ({len(merged_script)} chars)")

            # DEBUG: For Step 2, log the merged script
            if step_table_entry and step_table_entry.get('step_no') == '2':
                logger.info("=== DEBUG: Step 2 Merged Script ===")
                if merged_script:
                    # Log a summary of the merged script
                    merged_lines = merged_script.split('\n')
                    logger.info(f"Merged script length: {len(merged_lines)} lines")
                    logger.info("First 10 lines of merged script:")
                    for i, line in enumerate(merged_lines[:10]):
                        logger.info(f"  {i+1}: {line}")
                    if len(merged_lines) > 15:
                        logger.info("Last 5 lines of merged script:")
                        for i, line in enumerate(merged_lines[-5:]):
                            logger.info(f"  {len(merged_lines)-4+i}: {line}")
                else:
                    logger.warning("Merged script is empty or None after merging")
        else:
            logger.info("No previous script to merge with - using step-specific script as final output")

        # Update script continuity tracking if state is provided
        if state and step_table_entry:
            step_no = step_table_entry.get("step_no")
            if step_no:
                # DEBUG: Log state before update
                if step_no == '2':
                    logger.info("=== DEBUG: State Before Update ===")
                    if hasattr(state, 'previous_scripts'):
                        logger.info(f"State previous_scripts keys: {list(state.previous_scripts.keys()) if state.previous_scripts else 'None'}")
                    if hasattr(state, 'script_imports'):
                        logger.info(f"State script_imports count: {len(state.script_imports) if state.script_imports else 0}")
                    if hasattr(state, 'script_variables'):
                        logger.info(f"State script_variables keys: {list(state.script_variables.keys()) if state.script_variables else 'None'}")

                # Store the merged script (not the step-specific script) for continuity
                # Primary approach: use the update_script_continuity method if available
                if hasattr(state, "update_script_continuity"):
                    state.update_script_continuity(merged_script, step_no)
                    logger.info(f"Updated script continuity tracking for step {step_no}")

                    # DEBUG: Log state after update
                    if step_no == '2':
                        logger.info("=== DEBUG: State After Update ===")
                        if hasattr(state, 'previous_scripts'):
                            logger.info(f"State previous_scripts keys: {list(state.previous_scripts.keys()) if state.previous_scripts else 'None'}")
                        if hasattr(state, 'script_imports'):
                            logger.info(f"State script_imports count: {len(state.script_imports) if state.script_imports else 0}")
                        if hasattr(state, 'script_variables'):
                            logger.info(f"State script_variables keys: {list(state.script_variables.keys()) if state.script_variables else 'None'}")
                else:
                    # Fallback: at least remember the raw script
                    if not hasattr(state, "previous_scripts"):
                        state.previous_scripts = {}
                    state.previous_scripts[str(step_no)] = merged_script
                    logger.info(f"(fallback) Stored merged script for step {step_no} in state.previous_scripts")

        # =====================================================================
        # Final Script Preparation
        # =====================================================================
        # Add clear headers to both scripts to indicate their purpose and scope

        # Add a comment to the step-specific script to indicate it's only for the current step
        step_no = step_table_entry.get('step_no') if step_table_entry else 'unknown'
        step_specific_script = f"""# =========================================================================
# STEP-SPECIFIC SCRIPT - IMPLEMENTS ONLY STEP {step_no}
# =========================================================================
# This script focuses exclusively on implementing the functionality for
# test case step {step_no} without any attempt to merge with previous steps.
# It is useful for debugging and understanding the specific implementation
# of this individual step.
# =========================================================================

{step_specific_script}"""

        # Add a comment to the merged script to indicate it's the final merged script
        merged_script = f"""# =========================================================================
# MERGED SCRIPT - IMPLEMENTS ALL STEPS UP TO STEP {step_no}
# =========================================================================
# This script combines the implementation of all test case steps up to and
# including step {step_no}. It maintains proper continuity between steps,
# preserves browser state, and ensures a cohesive test flow.
# =========================================================================

{merged_script}"""

        # Return both the merged script and the step-specific script as a tuple
        # The merged script is returned first as it's the primary output used for execution
        return merged_script, step_specific_script
    except Exception as e:
        logger.error(f"Error generating test script: {e}")
        error_message = f"Error: {str(e)}"
        return error_message, error_message
